"""
Hub Studio API控制模块

提供与Hub Studio指纹浏览器的API交互功能。
支持浏览器环境的启动、关闭、状态查询等操作。

主要功能：
- 浏览器环境启动/关闭
- 连接测试和状态查询
- 完整的错误处理和重试机制
- WebDriver路径和调试端口获取
"""

import time
import json
from typing import Dict, Any, Optional, List, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..utils.logger import get_logger


class HubStudioAPIError(Exception):
    """Hub Studio API异常类"""
    
    def __init__(self, message: str, code: int = -1, data: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.code = code
        self.data = data or {}


class HubStudioAPI:
    """Hub Studio API控制器"""
    
    # API错误码映射
    ERROR_CODES = {
        0: "成功",
        5: "初始化代理失败",
        7: "启动内核失败", 
        15: "获取openDetail参数失败",
        17: "容器正在被占用",
        18: "取消",
        20: "不可打开状态",
        21: "获取ip超时",
        22: "转换userAgent失败",
        24: "openDetail超时",
        25: "获取磁盘信息失败",
        26: "API免费版本不支持",
        27: "环境打开次数超过限制",
        -10000: "未知异常",
        -10003: "登录失败",
        -10004: "未找到环境信息，请检查环境ID是否正确",
        -10005: "该店铺上次请求的startBrowser还未执行结束",
        -10007: "内核不存在，请手动打开客户端下载",
        -10008: "系统资源不足",
        -10011: "环境不存在或未开启，请检查环境ID是否正确",
        -10012: "插件ID列表不能为空",
        -10013: "环境正在运行",
        -10014: "IPC超时",
        -10015: "数据获取失败，请勿过于频繁操作，或检查网络环境后重试",
        -10016: "内核版本过低，本客户端不允许打开",
        -10017: "当前Firefox内核的环境无法通过API打开",
        -10018: "下载内核失败"
    }
    
    def __init__(self, api_base_url: str = "http://127.0.0.1:6873", timeout: int = 30,
                 app_id: str = None, app_secret: str = None):
        """
        初始化Hub Studio API控制器

        Args:
            api_base_url: API基础URL
            timeout: 请求超时时间（秒）
            app_id: Hub Studio APP ID
            app_secret: Hub Studio APP Secret
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.timeout = timeout
        self.app_id = app_id
        self.app_secret = app_secret
        self.logger = get_logger("hubstudio_api")

        # 配置HTTP会话
        self.session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'YouTube-Uploader/1.0.0'
        })

        # 如果提供了认证信息，添加到请求头
        if self.app_id and self.app_secret:
            self.session.headers.update({
                'App-Id': self.app_id,
                'App-Secret': self.app_secret
            })
            self.logger.info(f"Hub Studio API初始化完成 - 基础URL: {self.api_base_url} (已配置认证)")
        else:
            self.logger.info(f"Hub Studio API初始化完成 - 基础URL: {self.api_base_url} (未配置认证)")
            self.logger.warning("未配置APP ID和APP Secret，某些API可能需要认证")
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            
        Returns:
            响应数据
            
        Raises:
            HubStudioAPIError: API请求失败
        """
        url = f"{self.api_base_url}{endpoint}"
        
        try:
            self.logger.debug(f"发送{method}请求: {url}")
            if data:
                self.logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
            
            if method.upper() == "GET":
                response = self.session.get(url, params=data, timeout=self.timeout)
            else:
                response = self.session.post(url, json=data, timeout=self.timeout)
            
            response.raise_for_status()
            result = response.json()
            
            self.logger.debug(f"响应数据: {json.dumps(result, ensure_ascii=False)}")
            
            # 检查API返回的错误码
            if result.get('code', 0) != 0:
                error_code = result.get('code', -1)
                error_msg = self.ERROR_CODES.get(error_code, f"未知错误码: {error_code}")
                raise HubStudioAPIError(
                    f"API请求失败: {error_msg}",
                    code=error_code,
                    data=result.get('data', {})
                )
            
            return result
            
        except requests.exceptions.RequestException as e:
            error_msg = f"HTTP请求失败: {str(e)}"
            self.logger.error(error_msg)
            raise HubStudioAPIError(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"响应JSON解析失败: {str(e)}"
            self.logger.error(error_msg)
            raise HubStudioAPIError(error_msg)
    
    def test_connection(self) -> bool:
        """
        测试与Hub Studio的连接

        Returns:
            连接是否成功
        """
        try:
            # 尝试获取所有浏览器状态来测试连接
            self._make_request("POST", "/api/v1/browser/all-browser-status", {"containerCodes": []})
            self.logger.success("Hub Studio连接测试成功")
            return True
        except Exception as e:
            self.logger.error(f"Hub Studio连接测试失败: {str(e)}")
            return False

    def start_browser(
        self,
        container_code: str,
        headless: bool = False,
        readonly: bool = False,
        startup_urls: Optional[List[str]] = None,
        args: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        启动浏览器环境

        Args:
            container_code: 环境ID
            headless: 是否无头模式
            readonly: 是否只读模式
            startup_urls: 启动时打开的URL列表
            args: 浏览器启动参数

        Returns:
            启动结果，包含webdriver路径和调试端口

        Raises:
            HubStudioAPIError: 启动失败
        """
        data = {
            "containerCode": container_code,
            "isHeadless": headless,
            "isWebDriverReadOnlyMode": readonly
        }

        if startup_urls:
            data["containerTabs"] = startup_urls

        if args:
            data["args"] = args

        self.logger.info(f"启动浏览器环境: {container_code}")

        try:
            result = self._make_request("POST", "/api/v1/browser/start", data)
            browser_data = result.get('data', {})

            self.logger.success(f"浏览器环境启动成功 - 调试端口: {browser_data.get('debuggingPort')}")
            return browser_data

        except HubStudioAPIError as e:
            self.logger.error(f"启动浏览器环境失败: {e.message}")
            raise

    def stop_browser(self, container_code: str) -> bool:
        """
        关闭浏览器环境

        Args:
            container_code: 环境ID

        Returns:
            是否关闭成功

        Raises:
            HubStudioAPIError: 关闭失败
        """
        self.logger.info(f"关闭浏览器环境: {container_code}")

        try:
            self._make_request("POST", "/api/v1/browser/stop", {"containerCode": container_code})
            self.logger.success(f"浏览器环境关闭成功: {container_code}")
            return True

        except HubStudioAPIError as e:
            self.logger.error(f"关闭浏览器环境失败: {e.message}")
            raise

    def get_all_environments(self) -> List[Dict[str, Any]]:
        """
        获取所有环境列表

        Returns:
            环境信息列表，每个环境包含：
            - containerCode: 环境代码
            - name: 环境名称
        """
        try:
            self.logger.info("获取所有环境列表")

            # 由于API端点可能不存在，我们使用配置文件中的环境ID
            # 或者返回一个示例环境用于测试
            if hasattr(self, '_test_environments'):
                return self._test_environments

            # 如果没有配置，返回空列表，让用户手动配置
            self.logger.warning("未找到环境列表API，请在配置文件中手动配置environment_ids")
            return []

        except Exception as e:
            self.logger.error(f"获取环境列表失败: {e}")
            return []

    def get_environments_list(self) -> List[Dict[str, Any]]:
        """
        通过Hub Studio API获取浏览器环境列表

        Returns:
            环境信息列表，每个环境包含：
            - id: 环境ID
            - name: 环境名称
            - status: 环境状态
        """
        try:
            self.logger.info("通过Hub Studio API获取浏览器环境列表")

            # 使用官方文档中的正确端点
            url = f"{self.api_base_url}/api/v1/env/list"

            # 准备请求数据
            request_data = {
                "current": 1,
                "size": 200  # 最多200条
            }

            self.logger.debug(f"请求URL: {url}")
            self.logger.debug(f"请求数据: {request_data}")

            # 发送POST请求
            response = self.session.post(url, json=request_data, timeout=self.timeout)

            self.logger.debug(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                self.logger.debug(f"API响应数据: {data}")

                if data.get('code') == 0 and 'data' in data:
                    env_list = data['data'].get('list', [])

                    environments = []
                    for env in env_list:
                        env_info = {
                            'id': str(env.get('containerCode', '')),
                            'name': env.get('containerName', ''),
                            'status': self._determine_env_status(env),
                            'createTime': env.get('createTime', ''),
                            'lastOpenTime': env.get('allOpenTime', ''),
                            'proxyType': env.get('proxyTypeName', ''),
                            'lastUsedIp': env.get('lastUsedIp', ''),
                            'tagName': env.get('tagName', ''),
                            'remark': env.get('remark', '')
                        }
                        environments.append(env_info)

                    self.logger.info(f"成功获取 {len(environments)} 个环境")
                    return environments
                else:
                    error_msg = data.get('msg', '未知错误')
                    error_code = data.get('code', -1)
                    self.logger.error(f"API返回错误 (代码: {error_code}): {error_msg}")
                    return []
            else:
                # 记录错误响应
                try:
                    error_data = response.json()
                    self.logger.error(f"HTTP {response.status_code} 错误响应: {error_data}")
                except:
                    self.logger.error(f"HTTP {response.status_code} 错误响应: {response.text}")
                return []

        except requests.exceptions.RequestException as e:
            self.logger.error(f"API请求失败: {e}")
            return []
        except Exception as e:
            self.logger.error(f"获取环境列表失败: {e}")
            return []

    def _determine_env_status(self, env_data: Dict[str, Any]) -> str:
        """
        根据环境数据确定环境状态

        Args:
            env_data: 环境数据

        Returns:
            环境状态字符串
        """
        # 根据最后打开时间和其他信息判断状态
        last_open_time = env_data.get('allOpenTime', '')
        if last_open_time:
            return '已使用'
        else:
            return '未使用'

    def _try_endpoint_with_method(self, endpoint: str, method: str) -> Optional[List[Dict[str, Any]]]:
        """
        尝试使用指定HTTP方法访问端点

        Args:
            endpoint: API端点
            method: HTTP方法 (GET/POST)

        Returns:
            成功时返回环境列表，失败时返回None
        """
        try:
            url = f"{self.api_base_url}{endpoint}"
            self.logger.debug(f"尝试 {method} {url}")

            # 准备请求参数
            request_data = {}
            if method == 'POST':
                # 对于POST请求，可能需要一些基本参数
                request_data = {
                    "page": 1,
                    "limit": 100
                }

            # 发送请求
            if method == 'GET':
                response = self.session.get(url, timeout=self.timeout)
            elif method == 'POST':
                response = self.session.post(url, json=request_data, timeout=self.timeout)
            else:
                return None

            self.logger.debug(f"{method} {endpoint} 响应状态: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                self.logger.debug(f"{method} {endpoint} 响应数据: {data}")

                # 处理不同的响应格式
                environments = self._parse_browser_response(data)
                if environments is not None:
                    self.logger.info(f"成功获取 {len(environments)} 个环境 ({method} {endpoint})")
                    return environments
                else:
                    self.logger.debug(f"{method} {endpoint} 响应格式无法解析")
            else:
                # 记录非200状态码的响应
                try:
                    error_data = response.json()
                    self.logger.debug(f"{method} {endpoint} 错误响应: {error_data}")
                except:
                    self.logger.debug(f"{method} {endpoint} 错误响应: {response.text}")

            return None

        except requests.exceptions.ConnectionError as e:
            self.logger.debug(f"{method} {endpoint} 连接错误: {e}")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.debug(f"{method} {endpoint} 请求失败: {e}")
            return None
        except Exception as e:
            self.logger.debug(f"{method} {endpoint} 处理失败: {e}")
            return None

    def _parse_browser_response(self, data: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """
        解析浏览器API响应数据

        Args:
            data: API响应数据

        Returns:
            解析后的环境列表，如果解析失败返回None
        """
        try:
            environments = []

            # 格式1: {code: 0, data: {list: [...]}}
            if isinstance(data, dict) and data.get('code') == 0:
                if 'data' in data and isinstance(data['data'], dict):
                    browsers = data['data'].get('list', [])
                elif 'data' in data and isinstance(data['data'], list):
                    browsers = data['data']
                else:
                    browsers = []

            # 格式2: {success: true, data: [...]}
            elif isinstance(data, dict) and data.get('success'):
                browsers = data.get('data', [])

            # 格式3: 直接返回数组
            elif isinstance(data, list):
                browsers = data

            # 格式4: {status: "success", browsers: [...]}
            elif isinstance(data, dict) and data.get('status') == 'success':
                browsers = data.get('browsers', [])

            else:
                self.logger.debug(f"未识别的响应格式: {data}")
                return None

            # 解析浏览器数据
            for browser in browsers:
                if not isinstance(browser, dict):
                    continue

                env_info = {
                    'id': browser.get('id', browser.get('browser_id', browser.get('uuid', ''))),
                    'name': browser.get('name', browser.get('browser_name', browser.get('title', ''))),
                    'status': browser.get('status', browser.get('state', 'unknown')),
                    'group_id': browser.get('group_id', browser.get('groupId', '')),
                    'created_time': browser.get('created_time', browser.get('createTime', '')),
                    'updated_time': browser.get('updated_time', browser.get('updateTime', ''))
                }

                # 确保至少有ID或名称
                if env_info['id'] or env_info['name']:
                    environments.append(env_info)

            return environments

        except Exception as e:
            self.logger.error(f"解析浏览器响应失败: {e}")
            return None

    def _explore_api_structure(self):
        """探索Hub Studio API结构"""
        try:
            self.logger.info("探索Hub Studio API结构...")

            # 尝试访问根路径和常见路径
            explore_paths = [
                "/",
                "/api",
                "/api/v1",
                "/help",
                "/docs",
                "/swagger",
                "/api-docs"
            ]

            for path in explore_paths:
                try:
                    url = f"{self.api_base_url}{path}"
                    response = self.session.get(url, timeout=5)

                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')

                        if 'json' in content_type:
                            try:
                                data = response.json()
                                self.logger.info(f"发现JSON端点 {path}: {data}")
                            except:
                                self.logger.info(f"发现端点 {path} (JSON解析失败)")
                        else:
                            # 检查HTML内容中是否有API信息
                            text = response.text[:500]  # 只取前500字符
                            if 'api' in text.lower() or 'browser' in text.lower():
                                self.logger.info(f"发现可能的API信息端点 {path}")

                except Exception as e:
                    self.logger.debug(f"探索路径 {path} 失败: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"API结构探索失败: {e}")

    def get_environment_status(self, container_code: str) -> Dict[str, Any]:
        """
        获取特定环境的状态

        Args:
            container_code: 环境代码

        Returns:
            环境状态信息
        """
        try:
            status_map = self.get_browser_status([container_code])
            status = status_map.get(container_code, -1)

            return {
                'status': status,
                'statusText': self._get_status_text(status),
                'available': status in [0, 3]  # 已开启或已关闭状态都可以使用
            }

        except Exception as e:
            self.logger.error(f"获取环境 {container_code} 状态失败: {e}")
            return {'status': -1, 'statusText': '未知', 'available': False}

    def get_browser_status(self, container_codes: List[str]) -> Dict[str, int]:
        """
        获取浏览器状态

        Args:
            container_codes: 环境ID列表

        Returns:
            环境ID到状态的映射 (1-开启中, 0-已开启, 2-关闭中, 3-已关闭)

        Raises:
            HubStudioAPIError: 查询失败
        """
        try:
            result = self._make_request("POST", "/api/v1/browser/all-browser-status", {
                "containerCodes": container_codes
            })

            containers = result.get('data', {}).get('containers', [])
            status_map = {}

            for container in containers:
                container_code = container.get('containerCode')
                status = container.get('status')
                if container_code:
                    status_map[container_code] = status

            self.logger.debug(f"浏览器状态查询结果: {status_map}")
            return status_map

        except HubStudioAPIError as e:
            self.logger.error(f"查询浏览器状态失败: {e.message}")
            raise

    def get_all_open_browsers(self) -> List[Dict[str, Any]]:
        """
        获取所有已打开的浏览器环境

        Returns:
            已打开的浏览器环境列表

        Raises:
            HubStudioAPIError: 查询失败
        """
        try:
            result = self._make_request("POST", "/api/v1/browser/all-browser-status", {
                "containerCodes": []
            })

            containers = result.get('data', {}).get('containers', [])
            open_browsers = [c for c in containers if c.get('status') == 0]  # 0表示已开启

            self.logger.debug(f"已打开的浏览器数量: {len(open_browsers)}")
            return open_browsers

        except HubStudioAPIError as e:
            self.logger.error(f"查询已打开浏览器失败: {e.message}")
            raise

    def stop_all_browsers(self, clear_opening: bool = True) -> bool:
        """
        关闭所有浏览器环境

        Args:
            clear_opening: 是否清空启动队列

        Returns:
            是否关闭成功

        Raises:
            HubStudioAPIError: 关闭失败
        """
        self.logger.info("关闭所有浏览器环境")

        try:
            self._make_request("POST", "/api/v1/browser/stop-all", {
                "clearOpening": clear_opening
            })
            self.logger.success("所有浏览器环境关闭成功")
            return True

        except HubStudioAPIError as e:
            self.logger.error(f"关闭所有浏览器环境失败: {e.message}")
            raise

    def is_browser_running(self, container_code: str) -> bool:
        """
        检查指定浏览器是否正在运行

        Args:
            container_code: 环境ID

        Returns:
            是否正在运行
        """
        try:
            status_map = self.get_browser_status([container_code])
            status = status_map.get(container_code, 3)  # 默认为已关闭
            return status in [0, 1]  # 0-已开启, 1-开启中
        except Exception:
            return False

    def wait_for_browser_ready(self, container_code: str, max_wait_time: int = 60) -> bool:
        """
        等待浏览器环境准备就绪

        Args:
            container_code: 环境ID
            max_wait_time: 最大等待时间（秒）

        Returns:
            是否准备就绪
        """
        self.logger.info(f"等待浏览器环境准备就绪: {container_code}")

        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            try:
                status_map = self.get_browser_status([container_code])
                status = status_map.get(container_code, 3)

                if status == 0:  # 已开启
                    self.logger.success(f"浏览器环境准备就绪: {container_code}")
                    return True
                elif status == 3:  # 已关闭
                    self.logger.error(f"浏览器环境启动失败: {container_code}")
                    return False

                # 状态为1(开启中)或2(关闭中)，继续等待
                time.sleep(2)

            except Exception as e:
                self.logger.warning(f"检查浏览器状态时出错: {str(e)}")
                time.sleep(2)

        self.logger.error(f"等待浏览器环境超时: {container_code}")
        return False

    def get_all_available_environments(self, environment_ids: List[str]) -> List[Dict[str, Any]]:
        """
        获取所有可用的浏览器环境信息

        Args:
            environment_ids: 环境ID列表

        Returns:
            可用环境信息列表，每个环境包含ID、状态等信息

        Raises:
            HubStudioAPIError: 查询失败
        """
        try:
            self.logger.info(f"检查环境可用性: {environment_ids}")

            # 获取所有环境的状态
            status_map = self.get_browser_status(environment_ids)

            available_environments = []
            for env_id in environment_ids:
                status = status_map.get(env_id, -1)

                # 检查环境是否存在且可用
                if status != -1:  # -1表示环境不存在
                    env_info = {
                        'containerCode': env_id,
                        'status': status,
                        'statusText': self._get_status_text(status),
                        'available': status in [0, 3]  # 已开启或已关闭状态都可以使用
                    }
                    available_environments.append(env_info)
                    self.logger.debug(f"环境 {env_id}: {env_info['statusText']}")
                else:
                    self.logger.warning(f"环境 {env_id} 不存在或无法访问")

            self.logger.info(f"找到 {len(available_environments)} 个可用环境")
            return available_environments

        except HubStudioAPIError as e:
            self.logger.error(f"获取环境列表失败: {e.message}")
            raise

    def _get_status_text(self, status: int) -> str:
        """获取状态文本描述"""
        status_texts = {
            0: "已开启",
            1: "开启中",
            2: "关闭中",
            3: "已关闭"
        }
        return status_texts.get(status, f"未知状态({status})")

    def get_next_available_environment(self, environment_ids: List[str], exclude_running: bool = True) -> Optional[str]:
        """
        获取下一个可用的环境ID

        Args:
            environment_ids: 环境ID列表
            exclude_running: 是否排除正在运行的环境

        Returns:
            可用的环境ID，如果没有可用环境则返回None
        """
        try:
            available_envs = self.get_all_available_environments(environment_ids)

            for env in available_envs:
                if env['available']:
                    if exclude_running and env['status'] == 0:  # 排除已开启的环境
                        continue
                    return env['containerCode']

            # 如果没有找到关闭状态的环境，且允许使用运行中的环境
            if exclude_running:
                for env in available_envs:
                    if env['available']:
                        return env['containerCode']

            return None

        except Exception as e:
            self.logger.error(f"获取可用环境失败: {str(e)}")
            return None
