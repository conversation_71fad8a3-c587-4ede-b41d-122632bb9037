2025-08-02 06:26:10 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 06:26:10 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 06:26:10 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 06:26:11 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 06:26:11 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 06:26:11 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 06:26:11 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 06:26:11 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 06:26:11 | DEBUG    | src.api.hubstudio_api:get_all_open_browsers:297 | 已打开的浏览器数量: 0
2025-08-02 06:26:11 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 06:31:36 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 06:31:36 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 06:31:36 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 06:31:37 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 06:31:37 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 06:31:47 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:25:11 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:25:11 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:25:11 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 07:25:12 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:25:12 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 07:37:15 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:37:15 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:37:15 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 07:37:15 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:37:15 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 07:39:39 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:39:39 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:39:39 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 07:39:39 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:39:39 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 07:40:33 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:40:33 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 07:40:33 | INFO     | src.api.hubstudio_api:get_all_available_environments:397 | 检查环境可用性: ['example_env_1', 'example_env_2', 'example_env_3']
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_1", "example_env_2", "example_env_3"]}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_1", "status": 3}, {"containerCode": "example_env_2", "status": 3}, {"containerCode": "example_env_3", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_browser_status:272 | 浏览器状态查询结果: {'example_env_1': 3, 'example_env_2': 3, 'example_env_3': 3}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_1: 已关闭
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_2: 已关闭
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_3: 已关闭
2025-08-02 07:40:33 | INFO     | src.api.hubstudio_api:get_all_available_environments:419 | 找到 3 个可用环境
2025-08-02 07:40:33 | INFO     | src.api.hubstudio_api:get_all_available_environments:397 | 检查环境可用性: ['example_env_1', 'example_env_2', 'example_env_3']
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_1", "example_env_2", "example_env_3"]}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_1", "status": 3}, {"containerCode": "example_env_2", "status": 3}, {"containerCode": "example_env_3", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_browser_status:272 | 浏览器状态查询结果: {'example_env_1': 3, 'example_env_2': 3, 'example_env_3': 3}
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_1: 已关闭
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_2: 已关闭
2025-08-02 07:40:33 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_3: 已关闭
2025-08-02 07:40:33 | INFO     | src.api.hubstudio_api:get_all_available_environments:419 | 找到 3 个可用环境
2025-08-02 07:49:23 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:49:23 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:49:23 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:49:24 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 07:49:24 | INFO     | src.api.hubstudio_api:get_all_available_environments:397 | 检查环境可用性: ['example_env_1', 'example_env_2', 'example_env_3']
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_1", "example_env_2", "example_env_3"]}
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_1", "status": 3}, {"containerCode": "example_env_2", "status": 3}, {"containerCode": "example_env_3", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:get_browser_status:272 | 浏览器状态查询结果: {'example_env_1': 3, 'example_env_2': 3, 'example_env_3': 3}
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_1: 已关闭
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_2: 已关闭
2025-08-02 07:49:24 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_3: 已关闭
2025-08-02 07:49:24 | INFO     | src.api.hubstudio_api:get_all_available_environments:419 | 找到 3 个可用环境
2025-08-02 07:55:18 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 07:55:18 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:55:18 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:55:19 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 07:55:19 | INFO     | src.api.hubstudio_api:get_all_available_environments:397 | 检查环境可用性: ['example_env_1', 'example_env_2', 'example_env_3']
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_1", "example_env_2", "example_env_3"]}
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_1", "status": 3}, {"containerCode": "example_env_2", "status": 3}, {"containerCode": "example_env_3", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:get_browser_status:272 | 浏览器状态查询结果: {'example_env_1': 3, 'example_env_2': 3, 'example_env_3': 3}
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_1: 已关闭
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_2: 已关闭
2025-08-02 07:55:19 | DEBUG    | src.api.hubstudio_api:get_all_available_environments:415 | 环境 example_env_3: 已关闭
2025-08-02 07:55:19 | INFO     | src.api.hubstudio_api:get_all_available_environments:419 | 找到 3 个可用环境
2025-08-02 08:13:38 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:38 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:38 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:39 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:39 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:39 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:39 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:39 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:39 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:13:43 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:43 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:43 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:43 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:43 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:43 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:43 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:43 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:43 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:13:45 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:45 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:45 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:45 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:45 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:45 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:45 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:45 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:45 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:13:46 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:46 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:46 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:46 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:13:46 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:46 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:47 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:47 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:47 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:13:47 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:47 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:47 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:47 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:13:47 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:13:47 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:13:47 | INFO     | src.api.hubstudio_api:get_all_environments:255 | 获取所有环境列表
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送GET请求: http://127.0.0.1:6873/api/v1/browser/all-browsers
2025-08-02 08:13:47 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": "E010006", "msg": "请检查请求路径是否正确", "data": null}
2025-08-02 08:13:47 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006
2025-08-02 08:15:48 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:15:48 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_1"]}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_1", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:get_browser_status:321 | 浏览器状态查询结果: {'example_env_1': 3}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_2"]}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_2", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:get_browser_status:321 | 浏览器状态查询结果: {'example_env_2': 3}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": ["example_env_3"]}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "example_env_3", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:15:48 | DEBUG    | src.api.hubstudio_api:get_browser_status:321 | 浏览器状态查询结果: {'example_env_3': 3}
2025-08-02 08:26:35 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:26:35 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:26:35 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:26:36 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:26:36 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:26:36 | INFO     | src.api.hubstudio_api:get_environments_list:281 | 通过API获取环境列表
2025-08-02 08:26:36 | ERROR    | src.api.hubstudio_api:get_environments_list:317 | 获取环境列表失败: 'HubStudioAPI' object has no attribute 'base_url'
2025-08-02 08:27:48 | INFO     | src.api.hubstudio_api:__init__:101 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873
2025-08-02 08:27:48 | DEBUG    | src.api.hubstudio_api:_make_request:121 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:27:48 | DEBUG    | src.api.hubstudio_api:_make_request:123 | 请求数据: {"containerCodes": []}
2025-08-02 08:27:48 | DEBUG    | src.api.hubstudio_api:_make_request:133 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:27:48 | SUCCESS  | src.api.hubstudio_api:test_connection:166 | Hub Studio连接测试成功
2025-08-02 08:27:48 | INFO     | src.api.hubstudio_api:get_environments_list:281 | 通过API获取环境列表
2025-08-02 08:27:48 | ERROR    | src.api.hubstudio_api:get_environments_list:310 | API返回错误: 请检查请求路径是否正确
2025-08-02 08:50:03 | INFO     | src.api.hubstudio_api:__init__:114 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (未配置认证)
2025-08-02 08:50:03 | WARNING  | src.api.hubstudio_api:__init__:115 | 未配置APP ID和APP Secret，某些API可能需要认证
2025-08-02 08:50:03 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:50:03 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:50:04 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:50:04 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:04 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表
2025-08-02 08:50:20 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:50:20 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:50:20 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:20 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表
2025-08-02 08:50:23 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:50:23 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:50:23 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:23 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表
2025-08-02 08:50:23 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:50:23 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:50:24 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:50:24 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表
2025-08-02 08:50:24 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:50:24 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:50:24 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表
2025-08-02 08:50:24 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:50:24 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:50:24 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:309 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | API响应: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:372 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:50:24 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:52:02 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/v1/browser/list 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/v1/browser/list 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/browser/list 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/browser/list 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /browser/list 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /browser/list 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/v1/browsers 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/v1/browsers 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/browsers 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/browsers 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/v1/browser 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/v1/browser 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/browser
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/browser 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/browser 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/browser
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /browser 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /browser 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/v1/profile/list
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/v1/profile/list 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/v1/profile/list 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/api/profile/list
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /api/profile/list 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /api/profile/list 响应格式无法解析
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:315 | 尝试API端点: http://127.0.0.1:6873/profile/list
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:319 | 端点 /profile/list 响应状态: 200
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:323 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:396 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | DEBUG    | src.api.hubstudio_api:get_environments_list:331 | 端点 /profile/list 响应格式无法解析
2025-08-02 08:52:02 | ERROR    | src.api.hubstudio_api:get_environments_list:351 | 所有API端点都无法获取环境列表
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:426 | 探索Hub Studio API结构...
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /api: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /api/v1: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /help: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /docs: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /swagger: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:52:02 | INFO     | src.api.hubstudio_api:_explore_api_structure:450 | 发现JSON端点 /api-docs: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:14 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:53:14 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:53:14 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:53:15 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/profile
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/profile 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/profile 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/profile
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/profile 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/profile 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/profile
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /profile 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /profile 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/profile/list
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/profile/list 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/profile/list 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/profile/list
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/profile/list 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/profile/list 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/profile/list
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /profile/list 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /profile/list 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/profile
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/browser/profile 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/browser/profile 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/browser/profile
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/browser/profile 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/browser/profile 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/browser/profile
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /browser/profile 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /browser/profile 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/browser/list 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/browser/list 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/browser/list
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/browser/list 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/browser/list 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/browser/list
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /browser/list 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /browser/list 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/browsers 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/browsers 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/browsers
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/browsers 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/browsers 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/browser
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/browser 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/browser 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/browser
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/browser 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/browser 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/browser
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /browser 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /browser 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/environment
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/environment 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/environment 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/environment
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/environment 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/environment 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/environment
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /environment 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /environment 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/v1/env
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/v1/env 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/v1/env 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/api/env
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /api/env 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /api/env 响应格式无法解析
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:332 | 尝试API端点: http://127.0.0.1:6873/env
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:336 | 端点 /env 响应状态: 200
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:340 | API响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:413 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | DEBUG    | src.api.hubstudio_api:get_environments_list:348 | 端点 /env 响应格式无法解析
2025-08-02 08:53:15 | ERROR    | src.api.hubstudio_api:get_environments_list:368 | 所有API端点都无法获取环境列表
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:443 | 探索Hub Studio API结构...
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /api: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /api/v1: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /help: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /docs: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /swagger: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:53:15 | INFO     | src.api.hubstudio_api:_explore_api_structure:467 | 发现JSON端点 /api-docs: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:48 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 08:55:48 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 08:55:48 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 08:55:49 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过API获取浏览器环境列表
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/profile/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/profile/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/profile/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/profile/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/profile/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/profile/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/profile/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/profile/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/profile/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/profile/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/profile/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/profile/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/profile/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/profile/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/profile/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/profile/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/profile/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /profile/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /profile/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /profile/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/profile/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /profile/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /profile/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /profile/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/browser/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/browser/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/browser/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/browser/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/browser/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/browser/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/browser/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/browser/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/browser/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/browser/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/browser/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/browser/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/browser/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/browser/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/browser/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/browser/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/browser/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /browser/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /browser/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /browser/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/browser/profile
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /browser/profile 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /browser/profile 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /browser/profile 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/browser/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/browser/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/browser/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/browser/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/browser/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/browser/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/browser/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/browser/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/browser/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/browser/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/browser/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/browser/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/browser/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/browser/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/browser/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/browser/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /browser/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /browser/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /browser/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/browser/list
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /browser/list 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /browser/list 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /browser/list 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/browsers 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/browsers 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/browsers 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/browsers
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/browsers 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/browsers 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/browsers 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/browsers
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/browsers 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/browsers 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/browsers 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/browsers
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/browsers 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/browsers 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/browsers 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/browser
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/browser 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/browser 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/browser 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/browser
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/browser 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/browser 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/browser 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/browser
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/browser 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/browser 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/browser 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/browser
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/browser 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/browser 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/browser 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/browser
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /browser 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /browser 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /browser 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/browser
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /browser 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /browser 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /browser 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/environment
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/environment 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/environment 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/environment 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/environment
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/environment 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/environment 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/environment 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/environment
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/environment 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/environment 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/environment 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/environment
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/environment 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/environment 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/environment 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/environment
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /environment 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /environment 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /environment 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/environment
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /environment 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /environment 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /environment 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/v1/env
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/v1/env 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/v1/env 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/v1/env 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/v1/env
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/v1/env 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/v1/env 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/v1/env 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/api/env
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /api/env 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /api/env 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /api/env 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/api/env
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /api/env 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /api/env 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /api/env 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 GET http://127.0.0.1:6873/env
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | GET /env 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | GET /env 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | GET /env 响应格式无法解析
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:363 | 尝试 POST http://127.0.0.1:6873/env
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:382 | POST /env 响应状态: 200
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:386 | POST /env 响应数据: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_parse_browser_response:450 | 未识别的响应格式: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | DEBUG    | src.api.hubstudio_api:_try_endpoint_with_method:394 | POST /env 响应格式无法解析
2025-08-02 08:55:49 | ERROR    | src.api.hubstudio_api:get_environments_list:340 | 所有API端点都无法获取环境列表
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:480 | 探索Hub Studio API结构...
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /api: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /api/v1: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /help: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /docs: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /swagger: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 08:55:49 | INFO     | src.api.hubstudio_api:_explore_api_structure:504 | 发现JSON端点 /api-docs: {'code': 'E010006', 'msg': '请检查请求路径是否正确', 'data': None}
2025-08-02 09:08:38 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:08:38 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:08:38 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:08:38 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:08:38 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:08:38 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:08:38 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:08:38 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:08:39 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:08:39 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': 'c51719ae7f98f6c61a364927e4872107', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-01 22:19:35', 'allOpenTime': '08-01 22:19:35', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:08:39 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:09:14 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:09:14 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:09:14 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:09:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "51501", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 9429, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:09:24 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 51501
2025-08-02 09:09:24 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:09:24 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:09:24 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:09:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:09:24 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:09:24 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:09:27 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:09:27 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:09:27 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:09:27 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:09:27 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:09:27 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:09:28 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "51501", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 13276, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:09:28 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 51501
2025-08-02 09:09:28 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:09:28 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:09:28 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:09:28 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 2}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:09:28 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 2}
2025-08-02 09:09:29 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:09:29 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 3}
2025-08-02 09:09:30 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: **********
2025-08-02 09:09:30 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:09:30 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:09:30 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10004, "msg": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "data": {"statusCode": "-10004", "err": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "action": "stopBrowserByCode"}}
2025-08-02 09:09:30 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确
2025-08-02 09:09:35 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "51820", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4061, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:09:35 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 51820
2025-08-02 09:09:35 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:09:35 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:09:35 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:09:35 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:09:35 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:09:35 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:09:37 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:09:37 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:09:37 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:09:37 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:09:37 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:09:37 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:09:38 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "51820", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 7627, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:09:38 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 51820
2025-08-02 09:09:38 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:09:38 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:09:38 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:09:38 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 2}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:09:38 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 2}
2025-08-02 09:09:39 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:09:39 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:09:40 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:09:40 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:09:40 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:09:40 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 3}
2025-08-02 09:09:40 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: **********
2025-08-02 09:09:40 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:09:40 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:09:40 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:09:41 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10004, "msg": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "data": {"statusCode": "-10004", "err": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "action": "stopBrowserByCode"}}
2025-08-02 09:09:41 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确
2025-08-02 09:16:29 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:16:29 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:16:29 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:16:29 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:16:29 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:16:29 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:16:29 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:16:29 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:16:30 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:16:30 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': 'f29363207cf13b3620a2a0ac8c656605', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:09:32', 'allOpenTime': '08-02 09:09:31', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:16:30 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:16:42 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:16:42 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:16:42 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:16:47 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57353", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4526, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:16:47 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57353
2025-08-02 09:16:47 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:16:47 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:16:47 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:16:47 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:16:47 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:16:47 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:16:50 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:16:50 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:16:50 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:16:50 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:16:50 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:16:50 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:16:51 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57353", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 8166, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:16:51 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57353
2025-08-02 09:16:51 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:16:51 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:16:51 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:16:51 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 2}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:16:51 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 2}
2025-08-02 09:16:52 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:16:52 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 3}
2025-08-02 09:16:53 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: **********
2025-08-02 09:16:53 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:16:53 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:16:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:16:54 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10004, "msg": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "data": {"statusCode": "-10004", "err": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "action": "stopBrowserByCode"}}
2025-08-02 09:16:54 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确
2025-08-02 09:16:58 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57655", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4576, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:16:58 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57655
2025-08-02 09:16:58 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:16:58 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:16:58 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:16:58 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:16:58 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:16:58 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:17:01 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:17:01 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:17:01 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:17:01 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:17:01 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:17:01 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:17:02 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57655", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 8292, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:17:02 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57655
2025-08-02 09:17:02 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:17:02 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:17:02 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:17:02 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 2}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:17:02 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 2}
2025-08-02 09:17:03 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:17:03 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:17:04 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:17:04 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:17:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 3}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:17:04 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 3}
2025-08-02 09:17:04 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: **********
2025-08-02 09:17:04 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:17:04 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:17:04 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:17:05 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10004, "msg": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "data": {"statusCode": "-10004", "err": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "action": "stopBrowserByCode"}}
2025-08-02 09:17:05 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确
2025-08-02 09:27:31 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:27:31 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:27:31 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:27:31 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': 'f51c22add7155427c6265b4850c1079c', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:16:55', 'allOpenTime': '08-02 09:16:54', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:27:31 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:27:37 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:27:37 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:27:37 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:27:43 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "64191", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 5591, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:27:43 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 64191
2025-08-02 09:27:43 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:27:44 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:27:44 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:27:44 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:27:44 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:27:44 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:27:46 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:27:46 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:27:46 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:27:48 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:27:48 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:27:48 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:27:48 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:27:48 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:27:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "64534", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4483, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:27:53 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 64534
2025-08-02 09:27:53 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:27:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:27:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:27:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:27:53 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:27:53 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:27:56 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:27:56 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:27:56 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:27:58 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:27:58 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:27:58 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:27:58 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:27:58 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:28:03 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "64765", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4270, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:28:03 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 64765
2025-08-02 09:28:03 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:28:03 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:28:03 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:28:03 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:28:03 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:28:03 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:28:06 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:28:06 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:28:06 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:28:08 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:28:08 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:29:57 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:29:57 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:29:57 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:29:57 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '729f97f806fbab6214375c5dd98b0e50', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:28:00', 'allOpenTime': '08-02 09:27:59', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:29:57 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:30:04 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:30:04 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:30:04 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:30:09 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "50414", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4566, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:30:09 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 50414
2025-08-02 09:30:09 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:30:09 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:30:09 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:30:09 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:30:09 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:30:09 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:30:12 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:30:12 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:30:12 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:30:14 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:30:14 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:30:14 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:30:14 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:30:14 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:30:19 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "50821", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4209, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:30:19 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 50821
2025-08-02 09:30:19 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:30:19 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:30:19 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:30:19 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:30:19 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:30:19 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:30:22 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:30:22 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:30:22 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:30:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:30:24 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:30:24 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:30:24 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:30:24 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:30:29 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "51071", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4538, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:30:29 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 51071
2025-08-02 09:30:29 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:30:29 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:30:29 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:30:29 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:30:29 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:30:29 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:30:32 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:30:32 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:30:32 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:30:34 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:30:34 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:31:21 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:31:21 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:31:21 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:31:21 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:31:27 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "52221", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4245, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:31:27 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 52221
2025-08-02 09:31:27 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:31:27 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:31:27 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:31:27 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:31:27 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:31:27 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:31:35 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:31:35 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:31:35 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:31:36 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:31:36 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:32:14 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:32:14 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:32:14 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:32:14 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': 'ad68d9b39622402b39601ad8d503bd7f', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:31:23', 'allOpenTime': '08-02 09:31:23', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:32:14 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:32:19 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:32:19 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:32:19 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:32:25 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "53177", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4865, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:32:25 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 53177
2025-08-02 09:32:25 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:32:25 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:32:25 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:32:25 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:32:25 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:32:25 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:32:28 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:32:28 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:32:28 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:32:30 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:32:30 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:32:30 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:32:30 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:32:30 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:32:35 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "53469", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4482, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:32:35 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 53469
2025-08-02 09:32:35 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:32:35 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:32:35 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:32:35 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:32:35 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:32:35 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:32:38 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:32:38 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:32:38 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:32:40 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:32:40 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:38:58 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:38:58 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:38:58 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:38:58 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:39:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57847", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4877, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:39:04 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57847
2025-08-02 09:39:04 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:39:04 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:39:04 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:39:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:39:04 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:39:04 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:39:21 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:39:21 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:39:21 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:39:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:39:24 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:39:27 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:39:27 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:39:27 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:39:31 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "58308", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4045, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:39:31 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 58308
2025-08-02 09:39:31 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:39:31 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:39:31 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:39:31 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:39:31 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:39:31 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:39:45 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:39:45 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:39:45 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:39:47 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:39:47 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:39:47 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:39:47 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:39:47 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:39:48 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10004, "msg": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "data": {"statusCode": "-10004", "err": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "action": "stopBrowserByCode"}}
2025-08-02 09:39:48 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确
2025-08-02 09:40:27 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:40:27 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:40:27 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:40:27 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "59368", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4799, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:40:33 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 59368
2025-08-02 09:40:33 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:40:33 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:40:33 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:41:26 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:41:26 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:41:26 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:41:28 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "err": "成功(Success)", "action": "stopBrowserByCode"}}
2025-08-02 09:41:28 | SUCCESS  | src.api.hubstudio_api:stop_browser:252 | 浏览器环境关闭成功: **********
2025-08-02 09:41:52 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:41:52 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:41:52 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:41:52 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '00769cf4ae854cfcfd849186caa4da9a', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:40:29', 'allOpenTime': '08-02 09:40:29', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:41:52 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:41:58 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:41:58 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:41:58 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:42:03 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "60754", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4176, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:42:03 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 60754
2025-08-02 09:42:03 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:42:03 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:42:03 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:42:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:42:04 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:42:04 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:47:51 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:47:51 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:47:51 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 09:47:51 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 09:47:51 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 09:48:30 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:48:30 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 09:48:30 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 09:48:30 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '9658f20561c4ff5fdcb95ea7b44f0df3', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:42:00', 'allOpenTime': '08-02 09:42:00', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 09:48:30 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 09:48:33 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 09:48:33 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 09:48:33 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 09:48:39 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "65275", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4835, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 09:48:39 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 65275
2025-08-02 09:48:39 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 09:48:39 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:48:39 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 09:48:39 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:48:39 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 09:48:39 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 09:55:13 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 09:55:13 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 09:55:13 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 09:55:14 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 09:55:14 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:03:47 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:03:47 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:03:47 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 10:03:48 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:03:48 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:03:48 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 10:03:48 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 10:03:48 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 10:03:48 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 10:03:48 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '8401f8de561c8719aac29afb99dffe38', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 09:48:35', 'allOpenTime': '08-02 09:48:35', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 10:03:48 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 10:03:53 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:03:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:03:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:04:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "58254", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 10298, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 10:04:04 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 58254
2025-08-02 10:04:04 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 10:04:04 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:04:04 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 10:04:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:04:04 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 10:04:04 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 10:13:03 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:13:03 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:15:44 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:15:44 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:15:44 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 10:15:45 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:15:45 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:15:45 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 10:15:45 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 10:15:45 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 10:15:45 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 10:15:45 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': 'ab11b8905fba4f2de905b1cdc0149b44', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:03:55', 'allOpenTime': '08-02 10:03:54', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 10:15:45 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 10:15:48 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:15:48 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:15:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "65360", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4649, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 10:15:53 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 65360
2025-08-02 10:15:53 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 10:15:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:15:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 10:15:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:15:53 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 10:15:53 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 10:25:50 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:25:50 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:25:50 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 10:25:50 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:25:50 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:25:50 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 10:25:50 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 10:25:50 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 10:25:51 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 10:25:51 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '288ded640a7dc482c159f90a0d3091da', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:15:50', 'allOpenTime': '08-02 10:15:49', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 10:25:51 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 10:25:54 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:25:54 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:25:54 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 10:25:54 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '34f90f8d68c7e2830b134e75334cdb64', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:15:50', 'allOpenTime': '08-02 10:15:49', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 10:25:54 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 10:26:01 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:26:01 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:26:01 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:26:07 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "56664", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 5315, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 10:26:07 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 56664
2025-08-02 10:26:07 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 10:26:07 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:26:07 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 10:26:07 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:26:07 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 10:26:07 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 10:27:27 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:27:27 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:27:27 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 10:27:28 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:27:28 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:27:28 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 10:27:28 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 10:27:28 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 10:27:28 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 10:27:28 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '23ed819a30907d327d6006923755454e', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:26:03', 'allOpenTime': '08-02 10:26:02', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 1}}
2025-08-02 10:27:28 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 1 个环境
2025-08-02 10:29:00 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:29:00 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 10:29:00 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 10:29:00 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '2de994beda769c7f83c7fbb8569ca40f', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境5', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:28:46', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.178 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境4', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:28:31', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.99 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境3', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:28:15', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.7049.41 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境2', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:27:56', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.92 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:26:03', 'allOpenTime': '08-02 10:26:02', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 5}}
2025-08-02 10:29:00 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 5 个环境
2025-08-02 10:29:11 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:29:11 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:29:11 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:29:18 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "anmogdjgfaglaghniidhpdichjkmfghk", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "59259", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境5", "duplicate": 6094, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 10:29:18 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 59259
2025-08-02 10:29:18 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 10:29:18 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:29:18 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 10:29:18 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:29:18 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 10:29:18 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 10:40:41 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:40:41 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:40:41 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:40:41 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:40:41 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: 1281247137
2025-08-02 10:40:41 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:40:41 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "1281247137", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:40:41 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: 1281247136
2025-08-02 10:40:41 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:40:41 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "1281247136", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:40:42 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -1, "msg": "未找到环境信息，请检查环境ID是否正确", "data": {"errMessage": "未找到环境信息，请检查环境ID是否正确", "statusCode": "-1", "err": "未找到环境信息，请检查环境ID是否正确", "action": "startBrowserByCode"}}
2025-08-02 10:40:42 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1
2025-08-02 10:40:45 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: 1281247136
2025-08-02 10:40:45 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:40:45 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "1281247136", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:40:46 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -1, "msg": "未找到环境信息，请检查环境ID是否正确", "data": {"errMessage": "未找到环境信息，请检查环境ID是否正确", "statusCode": "-1", "err": "未找到环境信息，请检查环境ID是否正确", "action": "startBrowserByCode"}}
2025-08-02 10:40:46 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1
2025-08-02 10:40:52 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -1, "msg": "未找到环境信息，请检查环境ID是否正确", "data": {"errMessage": "未找到环境信息，请检查环境ID是否正确", "statusCode": "-1", "err": "未找到环境信息，请检查环境ID是否正确", "action": "startBrowserByCode"}}
2025-08-02 10:40:52 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1
2025-08-02 10:40:55 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:40:55 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:40:55 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:40:56 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "50386", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 12967, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 10:40:56 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 50386
2025-08-02 10:40:56 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 10:40:56 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:40:56 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 10:40:56 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:40:56 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 10:40:56 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 10:41:04 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -1, "msg": "未找到环境信息，请检查环境ID是否正确", "data": {"errMessage": "未找到环境信息，请检查环境ID是否正确", "statusCode": "-1", "err": "未找到环境信息，请检查环境ID是否正确", "action": "startBrowserByCode"}}
2025-08-02 10:41:04 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1
2025-08-02 10:41:07 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: 1281247137
2025-08-02 10:41:07 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:41:07 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "1281247137", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -1, "msg": "未找到环境信息，请检查环境ID是否正确", "data": {"errMessage": "未找到环境信息，请检查环境ID是否正确", "statusCode": "-1", "err": "未找到环境信息，请检查环境ID是否正确", "action": "startBrowserByCode"}}
2025-08-02 10:41:08 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1
2025-08-02 10:41:08 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 10:41:08 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "50386", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 25444, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 10:41:08 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 50386
2025-08-02 10:41:08 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 10:41:08 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 10:41:08 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:04:09 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:04:09 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 11:04:09 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 11:04:09 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '3f2f40eddcd0948c047bf718c798ed91', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境5', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:29:13', 'allOpenTime': '08-02 10:29:13', 'createTime': '2025-08-02 10:28:46', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.178 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境4', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:28:31', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.99 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境3', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:28:15', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.7049.41 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境2', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': None, 'lastCountry': None, 'lastRegion': None, 'lastCity': None, 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': None, 'allOpenTime': None, 'createTime': '2025-08-02 10:27:56', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.92 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 10:40:44', 'allOpenTime': '08-02 10:40:43', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 5}}
2025-08-02 11:04:09 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 5 个环境
2025-08-02 11:06:54 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:06:54 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:06:54 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:06:54 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:06:54 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:06:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:06:59 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "65067", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4949, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:06:59 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 65067
2025-08-02 11:07:00 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:07:00 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:07:00 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:07:00 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:07:00 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:07:00 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:07:05 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "kplagehdbhfkpfgcgdclodnjellhigbn", "browserID": 128247561, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "65261", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境2", "duplicate": 5491, "ip": "*************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": 128247561, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:07:05 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 65261
2025-08-02 11:07:05 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:07:05 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:07:05 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:07:05 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:07:05 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:07:05 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:07:10 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "anmogdjgfaglaghniidhpdichjkmfghk", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "65506", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境5", "duplicate": 4934, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:07:10 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 65506
2025-08-02 11:07:10 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:07:10 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:07:10 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:07:10 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:07:10 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:07:10 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:07:16 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "djenpjafboebedgknakmaabdhchnmcda", "browserID": 128247614, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "49697", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境3", "duplicate": 5799, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": 128247614, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:07:16 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 49697
2025-08-02 11:07:16 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:07:16 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:07:16 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:07:16 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:07:16 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:07:16 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:07:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "jmmhhcemkhjeboibdihbkplcofeafaed", "browserID": 128247643, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "49947", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境4", "duplicate": 8707, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": 128247643, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:07:24 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 49947
2025-08-02 11:07:24 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:07:24 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:07:24 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:07:25 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:07:25 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:07:25 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:38:45 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 11:38:45 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:38:45 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 11:38:57 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": "E010009", "msg": "登录已过期，请重新登录", "data": null}
2025-08-02 11:38:57 | ERROR    | src.api.hubstudio_api:test_connection:183 | Hub Studio连接测试失败: API请求失败: 未知错误码: E010009
2025-08-02 11:39:42 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 11:39:42 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:39:42 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 11:39:43 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:39:43 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 11:39:43 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 11:39:43 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 11:39:43 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 11:39:43 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 11:39:43 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': 'd7200ade739cd09b48a199b54a6fad3d', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境5', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:06', 'allOpenTime': '08-02 11:07:06', 'createTime': '2025-08-02 10:28:46', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.178 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境4', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:17', 'allOpenTime': '08-02 11:07:16', 'createTime': '2025-08-02 10:28:31', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.99 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境3', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:11', 'allOpenTime': '08-02 11:07:11', 'createTime': '2025-08-02 10:28:15', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.7049.41 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境2', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:01', 'allOpenTime': '08-02 11:07:00', 'createTime': '2025-08-02 10:27:56', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.92 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:06:56', 'allOpenTime': '08-02 11:06:55', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 5}}
2025-08-02 11:39:43 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 5 个环境
2025-08-02 11:39:52 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:39:52 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:39:52 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:39:57 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "anmogdjgfaglaghniidhpdichjkmfghk", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "54285", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境5", "duplicate": 4607, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:39:57 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 54285
2025-08-02 11:39:57 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:39:57 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:39:57 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:39:58 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:39:58 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:39:58 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:40:33 | INFO     | src.api.hubstudio_api:stop_browser:248 | 关闭浏览器环境: **********
2025-08-02 11:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/stop
2025-08-02 11:40:33 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********"}
2025-08-02 11:40:35 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:40:35 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:40:35 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:40:36 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10005, "msg": "该环境上次请求的startBrowser还未执行结束(The last request for startBrowser on this environment has not yet finished executing)", "data": {"statusCode": "-10005", "err": "该环境上次请求的startBrowser还未执行结束(The last request for startBrowser on this environment has not yet finished executing)", "action": "startBrowserByCode"}}
2025-08-02 11:40:36 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 该店铺上次请求的startBrowser还未执行结束
2025-08-02 11:40:37 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": -10004, "msg": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "data": {"statusCode": "-10004", "err": "未找到环境信息，请检查环境ID是否正确(Environment information not found, please check if the environment ID is correct)", "action": "stopBrowserByCode"}}
2025-08-02 11:40:37 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确
2025-08-02 11:40:39 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:40:39 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:40:39 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:40:40 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "anmogdjgfaglaghniidhpdichjkmfghk", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "55039", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境5", "duplicate": 4657, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:40:40 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 55039
2025-08-02 11:40:40 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:40:40 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:40:40 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:40:40 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:40:40 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:40:40 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:58:38 | INFO     | src.api.hubstudio_api:__init__:114 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (未配置认证)
2025-08-02 11:58:38 | WARNING  | src.api.hubstudio_api:__init__:115 | 未配置APP ID和APP Secret，某些API可能需要认证
2025-08-02 11:58:38 | INFO     | src.api.hubstudio_api:__init__:114 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (未配置认证)
2025-08-02 11:58:38 | WARNING  | src.api.hubstudio_api:__init__:115 | 未配置APP ID和APP Secret，某些API可能需要认证
2025-08-02 11:58:38 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 11:58:38 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:58:38 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:58:38 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:58:45 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "50058", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 5141, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:58:45 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 50058
2025-08-02 11:58:45 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:58:45 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:58:45 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:58:45 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:58:45 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:58:45 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 11:58:53 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 11:58:53 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 11:58:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 11:58:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 11:58:54 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "50058", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 14448, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 11:58:54 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 50058
2025-08-02 11:58:54 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 11:58:54 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 11:58:54 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 11:58:54 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 11:58:54 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 11:58:54 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 12:11:44 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 12:11:44 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 12:11:44 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 12:11:44 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 12:11:50 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "59548", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4898, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 12:11:50 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 59548
2025-08-02 12:11:50 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 12:11:50 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 12:11:50 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 12:11:50 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 12:11:50 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 12:11:50 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 12:17:35 | INFO     | src.api.hubstudio_api:__init__:114 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (未配置认证)
2025-08-02 12:17:35 | WARNING  | src.api.hubstudio_api:__init__:115 | 未配置APP ID和APP Secret，某些API可能需要认证
2025-08-02 12:18:23 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 12:18:23 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 12:18:23 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 12:18:23 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 12:18:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "59548", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 398839, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 12:18:24 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 59548
2025-08-02 12:18:24 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 12:18:24 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 12:18:24 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 12:18:24 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 12:18:24 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 12:18:24 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 12:47:23 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 12:47:23 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 12:47:23 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 12:47:23 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 12:47:23 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 12:47:29 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "61526", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4706, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 12:47:29 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 61526
2025-08-02 12:47:29 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 12:47:29 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 12:47:29 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 12:47:30 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 12:47:30 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 12:47:30 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 12:57:19 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 12:57:19 | INFO     | src.api.hubstudio_api:__init__:114 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (未配置认证)
2025-08-02 12:57:19 | WARNING  | src.api.hubstudio_api:__init__:115 | 未配置APP ID和APP Secret，某些API可能需要认证
2025-08-02 13:01:46 | INFO     | src.api.hubstudio_api:__init__:112 | Hub Studio API初始化完成 - 基础URL: http://127.0.0.1:6873 (已配置认证)
2025-08-02 13:01:46 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 13:01:46 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": []}
2025-08-02 13:01:47 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 13:01:47 | SUCCESS  | src.api.hubstudio_api:test_connection:180 | Hub Studio连接测试成功
2025-08-02 13:01:47 | INFO     | src.api.hubstudio_api:get_environments_list:295 | 通过Hub Studio API获取浏览器环境列表
2025-08-02 13:01:47 | DEBUG    | src.api.hubstudio_api:get_environments_list:306 | 请求URL: http://127.0.0.1:6873/api/v1/env/list
2025-08-02 13:01:47 | DEBUG    | src.api.hubstudio_api:get_environments_list:307 | 请求数据: {'current': 1, 'size': 200}
2025-08-02 13:01:47 | DEBUG    | src.api.hubstudio_api:get_environments_list:312 | 响应状态码: 200
2025-08-02 13:01:47 | DEBUG    | src.api.hubstudio_api:get_environments_list:316 | API响应数据: {'code': 0, 'msg': 'Success', 'requestId': '8f24602c0ce22aab90f42a9f6c6c9f99', 'timestamp': *************, 'data': {'list': [{'containerCode': **********, 'containerName': '新建环境5', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:40:36', 'allOpenTime': '08-02 11:40:36', 'createTime': '2025-08-02 10:28:46', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.178 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境4', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:17', 'allOpenTime': '08-02 11:07:16', 'createTime': '2025-08-02 10:28:31', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.99 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境3', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Chongqing', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:11', 'allOpenTime': '08-02 11:07:11', 'createTime': '2025-08-02 10:28:15', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.7049.41 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境2', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 11:07:01', 'allOpenTime': '08-02 11:07:00', 'createTime': '2025-08-02 10:27:56', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.92 Safari/537.36'}, {'containerCode': **********, 'containerName': '新建环境1', 'tagName': None, 'tagCode': None, 'remark': None, 'proxyTypeName': '不使用代理', 'proxyHost': None, 'proxyPort': None, 'proxyAccount': None, 'proxyPassword': '', 'asDynamicType': 0, 'lastUsedIp': '*************', 'lastCountry': 'France', 'lastRegion': 'Auvergne-Rhone-Alpes', 'lastCity': 'Lyon', 'referenceCountryName': None, 'referenceCountryCode': None, 'referenceRegionCode': None, 'referenceCity': None, 'referenceIp': '', 'openTime': '2025-08-02 12:47:26', 'allOpenTime': '08-02 12:47:25', 'createTime': '2025-07-27 00:37:17', 'coreVersion': 133, 'accounts': None, 'ipDatabaseChannel': 1, 'ipProtocolType': 1, 'refreshUrl': None, 'ua': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.48 Safari/537.36'}], 'total': 5}}
2025-08-02 13:01:47 | INFO     | src.api.hubstudio_api:get_environments_list:336 | 成功获取 5 个环境
2025-08-02 13:04:36 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 13:04:36 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 13:04:36 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 13:04:36 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 13:04:36 | INFO     | src.api.hubstudio_api:start_browser:222 | 启动浏览器环境: **********
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/start
2025-08-02 13:04:36 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCode": "**********", "isHeadless": false, "isWebDriverReadOnlyMode": false}
2025-08-02 13:04:42 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "jmmhhcemkhjeboibdihbkplcofeafaed", "browserID": 128247643, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "56720", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境4", "duplicate": 5891, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": 128247643, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 13:04:42 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 56720
2025-08-02 13:04:42 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 13:04:42 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 13:04:42 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 13:04:43 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 13:04:43 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 13:04:43 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 13:04:48 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "djenpjafboebedgknakmaabdhchnmcda", "browserID": 128247614, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "56912", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境3", "duplicate": 5727, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": 128247614, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 13:04:48 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 56912
2025-08-02 13:04:48 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 13:04:48 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 13:04:48 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 13:04:48 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 13:04:48 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 13:04:48 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 13:04:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "kplagehdbhfkpfgcgdclodnjellhigbn", "browserID": 128247561, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57120", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境2", "duplicate": 4852, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": 128247561, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 13:04:53 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57120
2025-08-02 13:04:53 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 13:04:53 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 13:04:53 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 13:04:53 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 13:04:53 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 13:04:53 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 13:04:57 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "ckcahannoiekjchdcocoefmlplefmkko", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57372", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境1", "duplicate": 4449, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 13:04:57 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57372
2025-08-02 13:04:58 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 13:04:58 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 13:04:58 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 13:04:58 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 13:04:58 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 13:04:58 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
2025-08-02 13:05:02 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"action": "startBrowserByCode", "backgroundPluginId": "anmogdjgfaglaghniidhpdichjkmfghk", "browserID": *********, "browserPath": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\hubstudio", "debuggingPort": "57566", "downloadPath": "C:\\Users\\<USER>\\Desktop\\Hubstudio\\新建环境5", "duplicate": 4626, "ip": "***************", "isDynamicIp": false, "launcherPage": "about:blank", "proxyType": "local", "runMode": 1, "webdriver": "C:\\Users\\<USER>\\AppData\\Local\\env-kit\\Core\\chrome_64_133_202505210948\\webdriver.exe", "containerId": *********, "containerCode": "**********", "statusCode": "0", "err": "成功(Success)"}}
2025-08-02 13:05:02 | SUCCESS  | src.api.hubstudio_api:start_browser:228 | 浏览器环境启动成功 - 调试端口: 57566
2025-08-02 13:05:02 | INFO     | src.api.hubstudio_api:wait_for_browser_ready:683 | 等待浏览器环境准备就绪: **********
2025-08-02 13:05:02 | DEBUG    | src.api.hubstudio_api:_make_request:135 | 发送POST请求: http://127.0.0.1:6873/api/v1/browser/all-browser-status
2025-08-02 13:05:02 | DEBUG    | src.api.hubstudio_api:_make_request:137 | 请求数据: {"containerCodes": ["**********"]}
2025-08-02 13:05:02 | DEBUG    | src.api.hubstudio_api:_make_request:147 | 响应数据: {"code": 0, "msg": "Success", "data": {"statusCode": "0", "containers": [{"containerCode": "**********", "status": 0}], "err": "成功(Success)", "action": "GetAllBrowserStatus"}}
2025-08-02 13:05:02 | DEBUG    | src.api.hubstudio_api:get_browser_status:597 | 浏览器状态查询结果: {'**********': 0}
2025-08-02 13:05:02 | SUCCESS  | src.api.hubstudio_api:wait_for_browser_ready:692 | 浏览器环境准备就绪: **********
