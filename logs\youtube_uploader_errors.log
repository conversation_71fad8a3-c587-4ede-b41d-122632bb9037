2025-08-02 08:13:39 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:13:43 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:13:45 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:13:46 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:13:47 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:13:47 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:13:47 | ERROR    | src.api.hubstudio_api:get_all_environments:279 | 获取环境列表失败: API请求失败: 未知错误码: E010006

2025-08-02 08:26:36 | ERROR    | src.api.hubstudio_api:get_environments_list:317 | 获取环境列表失败: 'HubStudioAPI' object has no attribute 'base_url'

2025-08-02 08:27:48 | ERROR    | src.api.hubstudio_api:get_environments_list:310 | API返回错误: 请检查请求路径是否正确

2025-08-02 08:50:04 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表

2025-08-02 08:50:20 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表

2025-08-02 08:50:23 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表

2025-08-02 08:50:24 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表

2025-08-02 08:50:24 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表

2025-08-02 08:50:24 | ERROR    | src.api.hubstudio_api:get_environments_list:330 | 所有API端点都无法获取环境列表

2025-08-02 08:52:02 | ERROR    | src.api.hubstudio_api:get_environments_list:351 | 所有API端点都无法获取环境列表

2025-08-02 08:53:15 | ERROR    | src.api.hubstudio_api:get_environments_list:368 | 所有API端点都无法获取环境列表

2025-08-02 08:55:49 | ERROR    | src.api.hubstudio_api:get_environments_list:340 | 所有API端点都无法获取环境列表

2025-08-02 09:09:27 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:09:30 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: 1281247135

2025-08-02 09:09:30 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: 浏览器环境启动超时

2025-08-02 09:09:30 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确

2025-08-02 09:09:37 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:09:40 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: 1281247135

2025-08-02 09:09:40 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: 浏览器环境启动超时

2025-08-02 09:09:40 | ERROR    | src.uploader.upload_manager:_handle_task_failure:482 | 任务执行失败 (已达最大重试次数): task_1_1754096954 - 连接浏览器环境失败

2025-08-02 09:09:41 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确

2025-08-02 09:16:50 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:16:53 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: 1281247135

2025-08-02 09:16:53 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: 浏览器环境启动超时

2025-08-02 09:16:54 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确

2025-08-02 09:17:01 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:17:04 | ERROR    | src.api.hubstudio_api:wait_for_browser_ready:695 | 浏览器环境启动失败: 1281247135

2025-08-02 09:17:04 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:181 | 连接浏览器环境失败: 浏览器环境启动超时

2025-08-02 09:17:04 | ERROR    | src.uploader.upload_manager:_handle_task_failure:472 | 任务执行失败 (已达最大重试次数): task_1_1754097402 - 连接浏览器环境失败

2025-08-02 09:17:05 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确

2025-08-02 09:27:46 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:184 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:27:56 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:184 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:28:06 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:184 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:30:12 | ERROR    | src.uploader.youtube_uploader:_create_driver:157 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:30:12 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:199 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:30:22 | ERROR    | src.uploader.youtube_uploader:_create_driver:157 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:30:22 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:199 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:30:32 | ERROR    | src.uploader.youtube_uploader:_create_driver:157 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:30:32 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:199 | 连接浏览器环境失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:32:28 | ERROR    | src.uploader.youtube_uploader:_create_driver:157 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:32:28 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:201 | WebDriver连接失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:32:28 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:215 | 连接浏览器环境失败: WebDriver连接失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:32:38 | ERROR    | src.uploader.youtube_uploader:_create_driver:157 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:32:38 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:201 | WebDriver连接失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:32:38 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:215 | 连接浏览器环境失败: WebDriver连接失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8F4A]
	(No symbol) [0x00007FF669DF79B5]
	(No symbol) [0x00007FF669DF9548]
	(No symbol) [0x00007FF669DF384A]
	(No symbol) [0x00007FF669E53432]
	(No symbol) [0x00007FF669E52C77]
	(No symbol) [0x00007FF669E54C46]
	(No symbol) [0x00007FF669E549F0]
	(No symbol) [0x00007FF669E46FC3]
	(No symbol) [0x00007FF669E0FEFE]
	(No symbol) [0x00007FF669E11183]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669F3B214]
	(No symbol) [0x00007FF669F3B3B6]
	(No symbol) [0x00007FF669F2A1E9]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 09:39:18 | ERROR    | src.uploader.youtube_uploader:_create_driver:206 | WebDriver创建失败，已尝试 3 次

2025-08-02 09:39:45 | ERROR    | src.uploader.youtube_uploader:_create_driver:206 | WebDriver创建失败，已尝试 3 次

2025-08-02 09:39:47 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:284 | 连接浏览器环境失败，已尝试 2 次

2025-08-02 09:39:48 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确

2025-08-02 09:43:37 | ERROR    | src.uploader.youtube_uploader:_click_create_button:710 | 未找到创建按钮

2025-08-02 10:13:02 | ERROR    | src.gui.modern_window:instant_cleanup:1334 | 即时清理时出错: main thread is not in main loop

2025-08-02 10:13:03 | CRITICAL | src.uploader.youtube_uploader:emergency_disconnect:426 | 紧急断开浏览器连接: 1281247135

2025-08-02 10:13:04 | CRITICAL | src.uploader.upload_manager:emergency_stop:262 | 紧急停止上传管理器

2025-08-02 10:13:04 | CRITICAL | src.gui.modern_window:emergency_exit:1273 | 紧急退出程序

2025-08-02 10:40:42 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1

2025-08-02 10:40:46 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1

2025-08-02 10:40:46 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:270 | 连接浏览器环境失败，已尝试 2 次

2025-08-02 10:40:46 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1281247136 初始化失败: 连接浏览器环境失败

2025-08-02 10:40:52 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1

2025-08-02 10:41:04 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1

2025-08-02 10:41:08 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 未知错误码: -1

2025-08-02 10:41:08 | ERROR    | src.uploader.youtube_uploader:connect_to_browser:270 | 连接浏览器环境失败，已尝试 2 次

2025-08-02 10:41:08 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1281247137 初始化失败: 连接浏览器环境失败

2025-08-02 11:07:39 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 11:07:39 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290475613 初始化失败: 导航到YouTube Studio失败

2025-08-02 11:07:43 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 11:07:43 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290476720 初始化失败: 导航到YouTube Studio失败

2025-08-02 11:07:50 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 11:07:50 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290476140 初始化失败: 导航到YouTube Studio失败

2025-08-02 11:07:58 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 11:07:58 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290476432 初始化失败: 导航到YouTube Studio失败

2025-08-02 11:38:57 | ERROR    | src.api.hubstudio_api:test_connection:183 | Hub Studio连接测试失败: API请求失败: 未知错误码: E010009

2025-08-02 11:40:30 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 11:40:36 | ERROR    | src.api.hubstudio_api:start_browser:232 | 启动浏览器环境失败: API请求失败: 该店铺上次请求的startBrowser还未执行结束

2025-08-02 11:40:37 | ERROR    | src.api.hubstudio_api:stop_browser:256 | 关闭浏览器环境失败: API请求失败: 未找到环境信息，请检查环境ID是否正确

2025-08-02 11:40:46 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:583 | 导航尝试 1 失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8D7C]
	(No symbol) [0x00007FF669E0F11F]
	(No symbol) [0x00007FF669E470B2]
	(No symbol) [0x00007FF669E41A49]
	(No symbol) [0x00007FF669E40AF9]
	(No symbol) [0x00007FF669D95595]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669D941AE]
	GetMachineCode [0x00007FF66A3C60D8+3271176]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 11:40:49 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:583 | 导航尝试 2 失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x00007FF669FD7EB5+84709]
	(No symbol) [0x00007FF669F34200]
	(No symbol) [0x00007FF669DC8D7C]
	(No symbol) [0x00007FF669E0F11F]
	(No symbol) [0x00007FF669E470B2]
	(No symbol) [0x00007FF669E41A49]
	(No symbol) [0x00007FF669E40AF9]
	(No symbol) [0x00007FF669D95595]
	GetMachineCode [0x00007FF66A30048D+2461117]
	GetMachineCode [0x00007FF66A353B03+2802739]
	GetMachineCode [0x00007FF66A34953D+2760301]
	GetMachineCode [0x00007FF66A0AED0A+30266]
	(No symbol) [0x00007FF669F3EC4F]
	(No symbol) [0x00007FF669D941AE]
	GetMachineCode [0x00007FF66A3C60D8+3271176]
	BaseThreadInitThunk [0x00007FFE1321E8D7+23]
	RtlUserThreadStart [0x00007FFE13E7C34C+44]


2025-08-02 11:58:38 | ERROR    | src.uploader.youtube_uploader:_handle_navigation_failure:632 | 此浏览器环境将被标记为不可用

2025-08-02 12:14:39 | ERROR    | src.uploader.youtube_uploader:_check_upload_error:1249 | ❌ 检测到错误: 處理程序已中斷

2025-08-02 12:14:39 | ERROR    | src.uploader.youtube_uploader:_wait_for_upload_complete:1156 | ❌ 检测到上传错误

2025-08-02 12:17:35 | ERROR    | src.uploader.youtube_uploader:_handle_navigation_failure:632 | 此浏览器环境将被标记为不可用

2025-08-02 12:23:28 | ERROR    | src.uploader.youtube_uploader:_wait_for_manual_login:1747 | 等待登录超时

2025-08-02 12:47:23 | ERROR    | src.uploader.youtube_uploader:_handle_navigation_failure:632 | 此浏览器环境将被标记为不可用

2025-08-02 12:47:23 | ERROR    | src.uploader.youtube_uploader:_handle_navigation_failure:632 | 此浏览器环境将被标记为不可用

2025-08-02 12:47:23 | ERROR    | src.uploader.youtube_uploader:_handle_navigation_failure:632 | 此浏览器环境将被标记为不可用

2025-08-02 12:48:48 | ERROR    | src.uploader.youtube_uploader:_wait_for_upload_complete:1156 | ❌ 检测到上传错误

2025-08-02 12:57:19 | ERROR    | src.uploader.youtube_uploader:_handle_navigation_failure:632 | 此浏览器环境将被标记为不可用

2025-08-02 13:05:14 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 13:05:14 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290476432 初始化失败: 导航到YouTube Studio失败

2025-08-02 13:05:19 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 13:05:19 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290476140 初始化失败: 导航到YouTube Studio失败

2025-08-02 13:05:25 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 13:05:25 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290475613 初始化失败: 导航到YouTube Studio失败

2025-08-02 13:05:32 | ERROR    | src.uploader.youtube_uploader:navigate_to_youtube_studio:579 | 导航到YouTube Studio失败

2025-08-02 13:05:32 | ERROR    | src.uploader.concurrent_env_manager:_initialize_single_environment:186 | 环境 1290476720 初始化失败: 导航到YouTube Studio失败

