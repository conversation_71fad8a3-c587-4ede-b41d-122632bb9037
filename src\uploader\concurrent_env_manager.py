"""
并发环境管理器模块

提供多浏览器环境的并发管理功能，支持环境池管理、
负载均衡和资源监控。

主要功能：
- 并发环境池管理
- 环境状态监控
- 负载均衡分配
- 环境资源清理
"""

import time
import threading
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from queue import Queue, Empty

from ..api.hubstudio_api import HubStudioAPI
from ..uploader.youtube_uploader import YouTubeUploader
from ..utils.logger import get_logger


class EnvironmentStatus(Enum):
    """环境状态枚举"""
    IDLE = "idle"           # 空闲
    BUSY = "busy"           # 忙碌
    CONNECTING = "connecting"  # 连接中
    ERROR = "error"         # 错误
    DISCONNECTED = "disconnected"  # 已断开


@dataclass
class EnvironmentInfo:
    """环境信息"""
    container_code: str
    status: EnvironmentStatus
    uploader: Optional[YouTubeUploader]
    current_task: Optional[str]
    last_activity: float
    error_count: int
    total_tasks: int


class ConcurrentEnvironmentManager:
    """并发环境管理器"""
    
    def __init__(
        self,
        hubstudio_api: HubStudioAPI,
        max_environments: int = 3,
        connection_timeout: int = 30
    ):
        """
        初始化并发环境管理器
        
        Args:
            hubstudio_api: Hub Studio API实例
            max_environments: 最大环境数量
            connection_timeout: 连接超时时间（秒）
        """
        self.hubstudio_api = hubstudio_api
        self.max_environments = max_environments
        self.connection_timeout = connection_timeout
        self.logger = get_logger("concurrent_env_manager")
        
        # 环境管理
        self.environments: Dict[str, EnvironmentInfo] = {}
        self.available_containers: List[str] = []
        self.task_queue: Queue = Queue()
        
        # 状态管理
        self.is_running = False
        self.is_initializing = False
        self._lock = threading.RLock()
        
        # 回调函数
        self.status_callback: Optional[Callable[[str, str, EnvironmentStatus], None]] = None
        self.progress_callback: Optional[Callable[[str, str, float], None]] = None
        
        self.logger.info(f"并发环境管理器初始化完成 - 最大环境数: {max_environments}")
    
    def set_available_containers(self, container_codes: List[str]) -> None:
        """设置可用的容器代码列表"""
        with self._lock:
            self.available_containers = container_codes[:self.max_environments]
            self.logger.info(f"设置可用容器: {self.available_containers}")
    
    def set_status_callback(self, callback: Callable[[str, str, EnvironmentStatus], None]) -> None:
        """设置状态回调函数"""
        self.status_callback = callback
    
    def set_progress_callback(self, callback: Callable[[str, str, float], None]) -> None:
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def initialize_environments(self) -> bool:
        """初始化所有环境"""
        if self.is_initializing:
            self.logger.warning("环境正在初始化中...")
            return False
        
        self.is_initializing = True
        self.logger.info("开始初始化并发环境...")
        
        try:
            # 清理现有环境
            self._cleanup_all_environments()
            
            # 并发初始化环境
            init_threads = []
            for container_code in self.available_containers:
                thread = threading.Thread(
                    target=self._initialize_single_environment,
                    args=(container_code,),
                    daemon=True
                )
                init_threads.append(thread)
                thread.start()
            
            # 等待所有环境初始化完成
            success_count = 0
            for i, thread in enumerate(init_threads):
                thread.join(timeout=self.connection_timeout)
                if thread.is_alive():
                    self.logger.warning(f"环境 {self.available_containers[i]} 初始化超时")
                else:
                    # 检查初始化结果
                    container_code = self.available_containers[i]
                    with self._lock:
                        if (container_code in self.environments and 
                            self.environments[container_code].status == EnvironmentStatus.IDLE):
                            success_count += 1
            
            self.logger.info(f"环境初始化完成: {success_count}/{len(self.available_containers)} 成功")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"环境初始化失败: {e}")
            return False
        finally:
            self.is_initializing = False
    
    def _initialize_single_environment(self, container_code: str) -> None:
        """初始化单个环境"""
        try:
            self.logger.info(f"初始化环境: {container_code}")
            
            # 创建环境信息
            with self._lock:
                self.environments[container_code] = EnvironmentInfo(
                    container_code=container_code,
                    status=EnvironmentStatus.CONNECTING,
                    uploader=None,
                    current_task=None,
                    last_activity=time.time(),
                    error_count=0,
                    total_tasks=0
                )
            
            # 通知状态变化
            self._notify_status_change(container_code, EnvironmentStatus.CONNECTING)
            
            # 创建上传器并连接
            uploader = YouTubeUploader(self.hubstudio_api)
            
            if uploader.connect_to_browser(container_code):
                # 导航到YouTube Studio
                if uploader.navigate_to_youtube_studio():
                    with self._lock:
                        env_info = self.environments[container_code]
                        env_info.uploader = uploader
                        env_info.status = EnvironmentStatus.IDLE
                        env_info.last_activity = time.time()
                    
                    self._notify_status_change(container_code, EnvironmentStatus.IDLE)
                    self.logger.success(f"环境 {container_code} 初始化成功")
                else:
                    raise Exception("导航到YouTube Studio失败")
            else:
                raise Exception("连接浏览器环境失败")
                
        except Exception as e:
            self.logger.error(f"环境 {container_code} 初始化失败: {e}")
            with self._lock:
                if container_code in self.environments:
                    self.environments[container_code].status = EnvironmentStatus.ERROR
                    self.environments[container_code].error_count += 1
            
            self._notify_status_change(container_code, EnvironmentStatus.ERROR)
    
    def get_idle_environment(self) -> Optional[str]:
        """获取空闲环境"""
        with self._lock:
            for container_code, env_info in self.environments.items():
                if env_info.status == EnvironmentStatus.IDLE:
                    return container_code
            return None
    
    def assign_task(self, container_code: str, task_id: str) -> bool:
        """分配任务到环境"""
        with self._lock:
            if (container_code in self.environments and 
                self.environments[container_code].status == EnvironmentStatus.IDLE):
                
                env_info = self.environments[container_code]
                env_info.status = EnvironmentStatus.BUSY
                env_info.current_task = task_id
                env_info.last_activity = time.time()
                env_info.total_tasks += 1
                
                self._notify_status_change(container_code, EnvironmentStatus.BUSY)
                return True
            return False
    
    def release_environment(self, container_code: str) -> None:
        """释放环境"""
        with self._lock:
            if container_code in self.environments:
                env_info = self.environments[container_code]
                env_info.status = EnvironmentStatus.IDLE
                env_info.current_task = None
                env_info.last_activity = time.time()
                
                self._notify_status_change(container_code, EnvironmentStatus.IDLE)
    
    def get_environment_uploader(self, container_code: str) -> Optional[YouTubeUploader]:
        """获取环境的上传器"""
        with self._lock:
            if container_code in self.environments:
                return self.environments[container_code].uploader
            return None
    
    def get_environment_stats(self) -> Dict[str, Dict]:
        """获取环境统计信息"""
        with self._lock:
            stats = {}
            for container_code, env_info in self.environments.items():
                stats[container_code] = {
                    'status': env_info.status.value,
                    'current_task': env_info.current_task,
                    'total_tasks': env_info.total_tasks,
                    'error_count': env_info.error_count,
                    'last_activity': env_info.last_activity
                }
            return stats
    
    def _cleanup_all_environments(self) -> None:
        """清理所有环境"""
        with self._lock:
            for container_code, env_info in self.environments.items():
                if env_info.uploader:
                    try:
                        env_info.uploader.disconnect_browser_safe(container_code)
                    except Exception as e:
                        self.logger.warning(f"清理环境 {container_code} 时出错: {e}")
            
            self.environments.clear()
    
    def _notify_status_change(self, container_code: str, status: EnvironmentStatus) -> None:
        """通知状态变化"""
        if self.status_callback:
            try:
                task_id = ""
                with self._lock:
                    if container_code in self.environments:
                        task_id = self.environments[container_code].current_task or ""
                
                self.status_callback(container_code, task_id, status)
            except Exception as e:
                self.logger.warning(f"状态回调失败: {e}")
    
    def shutdown(self) -> None:
        """关闭环境管理器"""
        self.logger.info("关闭并发环境管理器...")
        self.is_running = False
        self._cleanup_all_environments()
        self.logger.info("并发环境管理器已关闭")
