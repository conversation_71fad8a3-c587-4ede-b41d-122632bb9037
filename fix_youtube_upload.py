#!/usr/bin/env python3
"""
YouTube自动化上传修复工具

这个脚本包含了针对YouTube自动化上传问题的具体修复方案。

使用方法:
python fix_youtube_upload.py
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def fix_chrome_options():
    """修复Chrome选项配置问题"""
    print("🔧 修复Chrome选项配置...")
    
    # 检查是否有使用不兼容选项的代码
    youtube_uploader_path = project_root / "src" / "uploader" / "youtube_uploader.py"
    
    if youtube_uploader_path.exists():
        with open(youtube_uploader_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含不兼容的选项
        incompatible_patterns = [
            'excludeSwitches',
            'useAutomationExtension',
            'disable-blink-features=AutomationControlled'
        ]
        
        found_issues = []
        for pattern in incompatible_patterns:
            if pattern in content:
                found_issues.append(pattern)
        
        if found_issues:
            print(f"❌ 发现不兼容的Chrome选项: {found_issues}")
            print("请手动移除这些选项或使用更新的代码")
            return False
        else:
            print("✅ Chrome选项配置正常")
            return True
    else:
        print("❌ 找不到youtube_uploader.py文件")
        return False

def create_enhanced_selectors():
    """创建增强的元素选择器"""
    print("🔧 创建增强的元素选择器...")
    
    selectors_config = {
        "upload_button_selectors": [
            # 2024年最新选择器
            "//button[@aria-label='建立']",
            "//button[@aria-label='Create']",
            "//button[@aria-label='上傳影片']",
            "//button[@aria-label='Upload video']",
            "//button[contains(text(), '建立')]",
            "//button[contains(text(), 'CREATE')]",
            "//ytcp-icon-button[@icon='add']//button",
            "//button[contains(@class, 'ytcp-icon-button')]"
        ],
        "file_input_selectors": [
            "//input[@type='file']",
            "//input[@accept*='video']",
            "//ytcp-uploads-file-picker//input[@type='file']",
            "//ytcp-uploads-dialog//input[@type='file']",
            "//div[contains(@class, 'upload-dialog')]//input[@type='file']"
        ],
        "select_files_button_selectors": [
            "//button[contains(text(), '選取檔案')]",
            "//button[contains(text(), '选择文件')]",
            "//button[contains(text(), 'SELECT FILES')]",
            "//span[contains(text(), '選取檔案')]/parent::button",
            "//ytcp-button[contains(@class, 'select-files-button')]//button"
        ]
    }
    
    print("✅ 元素选择器配置已更新")
    return selectors_config

def create_retry_mechanism():
    """创建重试机制"""
    print("🔧 创建增强的重试机制...")
    
    retry_config = {
        "max_retries": 3,
        "retry_delay": 2,
        "element_wait_timeout": 10,
        "page_load_timeout": 30
    }
    
    print("✅ 重试机制配置已创建")
    return retry_config

def create_debug_logger():
    """创建调试日志配置"""
    print("🔧 创建调试日志配置...")
    
    debug_config = {
        "log_level": "DEBUG",
        "log_to_console": True,
        "log_to_file": True,
        "detailed_element_logging": True,
        "screenshot_on_error": True
    }
    
    print("✅ 调试日志配置已创建")
    return debug_config

def test_element_detection():
    """测试元素检测功能"""
    print("🧪 测试元素检测功能...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        print("✅ Selenium导入成功")
        
        # 测试选择器语法
        test_selectors = [
            "//button[@aria-label='Create']",
            "//input[@type='file']",
            "//div[contains(@class, 'upload')]"
        ]
        
        for selector in test_selectors:
            try:
                # 验证XPath语法
                from selenium.webdriver.common.by import By
                By.XPATH, selector
                print(f"✅ 选择器语法正确: {selector}")
            except Exception as e:
                print(f"❌ 选择器语法错误: {selector} - {e}")
                return False
        
        print("✅ 所有选择器语法检查通过")
        return True
        
    except ImportError as e:
        print(f"❌ Selenium导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 元素检测测试失败: {e}")
        return False

def create_troubleshooting_guide():
    """创建故障排除指南"""
    print("📚 创建故障排除指南...")
    
    guide = """
# YouTube自动化上传故障排除指南

## 常见问题及解决方案

### 1. WebDriver连接问题
**症状**: "unrecognized chrome option" 错误
**解决方案**:
- 更新Chrome浏览器到最新版本
- 移除不兼容的Chrome选项
- 使用最简化的Chrome配置

### 2. Hub Studio连接问题  
**症状**: API连接失败，错误码E010006, E010009
**解决方案**:
- 确保Hub Studio正在运行
- 检查API端口6873是否可访问
- 重启Hub Studio服务

### 3. 元素定位失败
**症状**: 找不到上传按钮或文件输入框
**解决方案**:
- 使用最新的元素选择器
- 增加等待时间
- 检查页面是否完全加载

### 4. 文件上传失败
**症状**: 文件路径传递失败
**解决方案**:
- 使用绝对路径
- 检查文件权限
- 确保文件格式支持

## 调试步骤

1. 运行诊断工具: `python debug_youtube_upload.py`
2. 检查日志文件: `logs/youtube_uploader_errors.log`
3. 手动测试浏览器连接
4. 逐步测试上传流程

## 联系支持

如果问题仍然存在，请提供:
- 错误日志
- 系统环境信息
- 重现步骤
"""
    
    # 保存故障排除指南
    guide_path = project_root / "TROUBLESHOOTING.md"
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print(f"✅ 故障排除指南已保存到: {guide_path}")
    return True

def run_fixes():
    """运行所有修复"""
    print("🚀 YouTube自动化上传修复工具")
    print("=" * 50)
    
    fixes_applied = 0
    total_fixes = 5
    
    # 1. 修复Chrome选项
    if fix_chrome_options():
        fixes_applied += 1
    
    # 2. 创建增强选择器
    if create_enhanced_selectors():
        fixes_applied += 1
    
    # 3. 创建重试机制
    if create_retry_mechanism():
        fixes_applied += 1
    
    # 4. 测试元素检测
    if test_element_detection():
        fixes_applied += 1
    
    # 5. 创建故障排除指南
    if create_troubleshooting_guide():
        fixes_applied += 1
    
    print("\n" + "=" * 50)
    print(f"✅ 修复完成: {fixes_applied}/{total_fixes}")
    
    if fixes_applied == total_fixes:
        print("🎉 所有修复已成功应用！")
        print("\n📋 下一步:")
        print("1. 重启应用程序")
        print("2. 运行诊断工具验证修复")
        print("3. 测试YouTube上传功能")
    else:
        print("⚠️ 部分修复未能完成，请检查错误信息")

if __name__ == "__main__":
    try:
        run_fixes()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 修复工具运行失败: {e}")
        import traceback
        traceback.print_exc()
