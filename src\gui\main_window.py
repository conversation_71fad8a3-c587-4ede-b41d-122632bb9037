"""
主窗口GUI模块

使用CustomTkinter创建现代化的YouTube自动化上传工具界面。
包含配置面板、状态面板和操作控制等功能。

界面布局：
- 左侧：配置面板（视频信息、API配置、文件选择、操作控制）
- 右侧：状态面板（浏览器状态、并发控制、进度显示、日志）
"""

import os
import json
import time
import tkinter as tk
from tkinter import filedialog, messagebox
from typing import List, Dict, Any, Optional
from pathlib import Path
import customtkinter as ctk

from ..api.hubstudio_api import HubStudioAPI
from ..uploader.upload_manager import UploadManager, TaskStatus
from ..utils.logger import setup_logger, get_logger
from .styles import (
    COLORS, ICONS, BUTTON_STYLES, LABEL_STYLES, FRAME_STYLES,
    apply_button_style, apply_label_style, apply_frame_style, get_icon, get_color, get_font
)


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        # 设置CustomTkinter主题
        ctk.set_appearance_mode("light")  # 改为浅色主题，更易识别
        ctk.set_default_color_theme("blue")

        # 初始化日志
        setup_logger()
        self.logger = get_logger("main_window")

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("YouTube自动化视频上传工具 v1.0.0")
        self.root.geometry("1400x900")  # 增大窗口尺寸
        self.root.minsize(1200, 800)

        # 配置网格权重
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=1)

        # 初始化变量
        self.selected_files: List[str] = []
        self.hubstudio_api: Optional[HubStudioAPI] = None
        self.upload_manager: Optional[UploadManager] = None
        self.is_uploading = False

        # 配置变量
        self.api_url_var = ctk.StringVar(value="http://127.0.0.1:6873")
        self.title_var = ctk.StringVar()
        self.description_var = ctk.StringVar()
        self.concurrent_var = ctk.IntVar(value=3)

        # 环境管理
        self.available_environments: List[Dict[str, Any]] = []
        self.environment_ids: List[str] = []  # 从配置文件加载

        # 创建界面
        self._create_widgets()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        self.logger.info("主窗口初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建标题栏
        self._create_title_bar()

        # 创建主内容区域
        self._create_main_content()

        # 创建状态栏
        self._create_status_bar()

    def _create_title_bar(self):
        """创建标题栏"""
        title_frame = ctk.CTkFrame(self.root, height=60, corner_radius=0)
        title_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        title_frame.grid_columnconfigure(1, weight=1)
        title_frame.grid_propagate(False)

        # 应用图标和标题
        title_label = ctk.CTkLabel(
            title_frame,
            text="🎬 YouTube自动化视频上传工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # 版本信息
        version_label = ctk.CTkLabel(
            title_frame,
            text="v1.0.0",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        version_label.grid(row=0, column=1, padx=20, pady=15, sticky="e")

    def _create_main_content(self):
        """创建主内容区域"""
        # 主内容框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        main_frame.grid_columnconfigure((0, 1), weight=1)
        main_frame.grid_rowconfigure(0, weight=1)

        # 左侧配置面板
        self._create_left_panel(main_frame)

        # 右侧状态面板
        self._create_right_panel(main_frame)

    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ctk.CTkFrame(self.root, height=30, corner_radius=0)
        status_frame.grid(row=2, column=0, sticky="ew", padx=0, pady=0)
        status_frame.grid_columnconfigure(1, weight=1)
        status_frame.grid_propagate(False)

        # 状态信息
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="就绪",
            font=ctk.CTkFont(size=11)
        )
        self.status_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        # 时间显示
        import time
        time_label = ctk.CTkLabel(
            status_frame,
            text=time.strftime("%Y-%m-%d %H:%M:%S"),
            font=ctk.CTkFont(size=11),
            text_color="gray"
        )
        time_label.grid(row=0, column=1, padx=10, pady=5, sticky="e")
    
    def _create_left_panel(self, parent):
        """创建左侧配置面板"""
        # 左侧主框架
        left_frame = ctk.CTkScrollableFrame(parent, label_text="📝 配置设置")
        left_frame.grid(row=0, column=0, padx=(10, 5), pady=10, sticky="nsew")
        left_frame.grid_columnconfigure(0, weight=1)

        # API配置区域
        self._create_api_config_section(left_frame, 0)

        # 视频信息区域
        self._create_video_info_section(left_frame, 1)

        # 文件选择区域
        self._create_file_selection_section(left_frame, 2)

        # 操作控制区域
        self._create_control_section(left_frame, 3)
    
    def _create_video_info_section(self, parent, row):
        """创建视频信息区域"""
        # 区域框架
        info_frame = ctk.CTkFrame(parent)
        info_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        info_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        info_title = ctk.CTkLabel(
            info_frame,
            text="📹 视频信息",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        info_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(info_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure(0, weight=1)

        # 标题输入
        title_label = ctk.CTkLabel(
            content_frame,
            text="视频标题 *",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.grid(row=0, column=0, padx=15, pady=(15, 5), sticky="w")

        self.title_entry = ctk.CTkEntry(
            content_frame,
            textvariable=self.title_var,
            height=40,
            font=ctk.CTkFont(size=13)
        )
        self.title_entry.grid(row=1, column=0, padx=15, pady=(0, 5), sticky="ew")

        title_hint = ctk.CTkLabel(
            content_frame,
            text="请输入视频标题（必填）",
            text_color="gray",
            font=ctk.CTkFont(size=11)
        )
        title_hint.grid(row=2, column=0, padx=15, pady=(0, 15), sticky="w")

        # 描述输入
        desc_label = ctk.CTkLabel(
            content_frame,
            text="视频描述",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        desc_label.grid(row=3, column=0, padx=15, pady=(0, 5), sticky="w")

        self.description_text = ctk.CTkTextbox(
            content_frame,
            height=120,
            font=ctk.CTkFont(size=12)
        )
        self.description_text.grid(row=4, column=0, padx=15, pady=(0, 5), sticky="ew")

        desc_hint = ctk.CTkLabel(
            content_frame,
            text="输入视频描述（可选，支持多行文本）",
            text_color="gray",
            font=ctk.CTkFont(size=11)
        )
        desc_hint.grid(row=5, column=0, padx=15, pady=(0, 15), sticky="w")
    
    def _create_api_config_section(self, parent, row):
        """创建API配置区域"""
        # 区域框架
        api_frame = ctk.CTkFrame(parent)
        api_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        api_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        api_title = ctk.CTkLabel(
            api_frame,
            text="🔗 Hub Studio连接",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        api_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(api_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure(1, weight=1)

        # API地址
        api_url_label = ctk.CTkLabel(
            content_frame,
            text="API地址:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        api_url_label.grid(row=0, column=0, padx=15, pady=(15, 5), sticky="w")

        self.api_url_entry = ctk.CTkEntry(
            content_frame,
            textvariable=self.api_url_var,
            height=35,
            font=ctk.CTkFont(size=12)
        )
        self.api_url_entry.grid(row=0, column=1, padx=(10, 15), pady=(15, 5), sticky="ew")

        # 连接测试按钮
        test_btn = ctk.CTkButton(
            content_frame,
            text="🔍 测试连接并获取环境",
            command=self._test_connection,
            height=35,
            font=ctk.CTkFont(size=13, weight="bold")
        )
        test_btn.grid(row=1, column=0, columnspan=2, padx=15, pady=(10, 5), sticky="ew")

        # 连接状态
        self.connection_status = ctk.CTkLabel(
            content_frame,
            text="● 未连接",
            text_color="red",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.connection_status.grid(row=2, column=0, columnspan=2, padx=15, pady=(5, 15))
    
    def _create_file_selection_section(self, parent, row):
        """创建文件选择区域"""
        # 区域框架
        file_frame = ctk.CTkFrame(parent)
        file_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        file_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        file_title = ctk.CTkLabel(
            file_frame,
            text="📁 视频文件",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        file_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(file_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure(0, weight=1)

        # 选择文件按钮
        select_btn = ctk.CTkButton(
            content_frame,
            text="📂 选择视频文件",
            command=self._select_files,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        select_btn.grid(row=0, column=0, padx=15, pady=15, sticky="ew")

        # 文件列表标签
        list_label = ctk.CTkLabel(
            content_frame,
            text="已选择的文件:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        list_label.grid(row=1, column=0, padx=15, pady=(0, 5), sticky="w")

        # 文件列表框架
        list_frame = ctk.CTkFrame(content_frame)
        list_frame.grid(row=2, column=0, padx=15, pady=(0, 15), sticky="ew")
        list_frame.grid_columnconfigure(0, weight=1)

        # 文件列表
        self.file_listbox = tk.Listbox(
            list_frame,
            height=8,
            bg="white",
            fg="black",
            selectbackground="#0078d4",
            font=("Segoe UI", 10),
            relief="flat",
            borderwidth=0
        )
        self.file_listbox.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        # 滚动条
        scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky="ns", pady=5)
        self.file_listbox.config(yscrollcommand=scrollbar.set)

        # 文件格式提示
        format_hint = ctk.CTkLabel(
            content_frame,
            text="支持格式: MP4, AVI, MOV, MKV, WMV, FLV, WEBM, M4V",
            text_color="gray",
            font=ctk.CTkFont(size=11)
        )
        format_hint.grid(row=3, column=0, padx=15, pady=(0, 15), sticky="w")
    
    def _create_control_section(self, parent, row):
        """创建操作控制区域"""
        # 区域框架
        control_frame = ctk.CTkFrame(parent)
        control_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        control_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        control_title = ctk.CTkLabel(
            control_frame,
            text="🎮 操作控制",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        control_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(control_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure((0, 1, 2), weight=1)

        # 开始上传按钮
        self.start_btn = ctk.CTkButton(
            content_frame,
            text="🚀 开始上传",
            fg_color="#28a745",
            hover_color="#218838",
            command=self._start_upload,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.start_btn.grid(row=0, column=0, padx=10, pady=15, sticky="ew")

        # 暂停/恢复按钮
        self.pause_btn = ctk.CTkButton(
            content_frame,
            text="⏸️ 暂停",
            fg_color="#ffc107",
            hover_color="#e0a800",
            text_color="black",
            command=self._toggle_pause,
            state="disabled",
            height=45,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.pause_btn.grid(row=0, column=1, padx=10, pady=15, sticky="ew")

        # 停止上传按钮
        self.stop_btn = ctk.CTkButton(
            content_frame,
            text="⏹️ 停止上传",
            fg_color="#dc3545",
            hover_color="#c82333",
            command=self._stop_upload,
            state="disabled",
            height=45,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.stop_btn.grid(row=0, column=2, padx=10, pady=15, sticky="ew")

    def _create_right_panel(self, parent):
        """创建右侧状态面板"""
        # 右侧主框架
        right_frame = ctk.CTkScrollableFrame(parent, label_text="📊 状态监控")
        right_frame.grid(row=0, column=1, padx=(5, 10), pady=10, sticky="nsew")
        right_frame.grid_columnconfigure(0, weight=1)

        # 环境列表区域
        self._create_environment_list_section(right_frame, 0)

        # 并发控制区域
        self._create_concurrent_control_section(right_frame, 1)

        # 上传进度显示区域
        self._create_progress_section(right_frame, 2)

        # 日志显示区域
        self._create_log_section(right_frame, 3)

    def _create_environment_list_section(self, parent, row):
        """创建环境列表区域"""
        # 区域框架
        env_frame = ctk.CTkFrame(parent)
        env_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        env_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        env_title = ctk.CTkLabel(
            env_frame,
            text="🌐 浏览器环境",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        env_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(env_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure(0, weight=1)

        # 环境状态标签
        status_label = ctk.CTkLabel(
            content_frame,
            text="环境状态:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        status_label.grid(row=0, column=0, padx=15, pady=(15, 5), sticky="w")

        # 环境列表框架
        list_frame = ctk.CTkFrame(content_frame)
        list_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        list_frame.grid_columnconfigure(0, weight=1)

        # 环境列表
        self.environment_listbox = tk.Listbox(
            list_frame,
            height=6,
            bg="white",
            fg="black",
            selectbackground="#0078d4",
            font=("Segoe UI", 10),
            relief="flat",
            borderwidth=0
        )
        self.environment_listbox.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        # 滚动条
        env_scrollbar = tk.Scrollbar(list_frame, orient="vertical", command=self.environment_listbox.yview)
        env_scrollbar.grid(row=0, column=1, sticky="ns", pady=5)
        self.environment_listbox.config(yscrollcommand=env_scrollbar.set)

        # 环境统计
        self.env_stats_label = ctk.CTkLabel(
            content_frame,
            text="未检测到环境",
            text_color="gray",
            font=ctk.CTkFont(size=11)
        )
        self.env_stats_label.grid(row=2, column=0, padx=15, pady=(0, 15), sticky="w")

    def _create_concurrent_control_section(self, parent, row):
        """创建并发控制区域"""
        # 区域框架
        concurrent_frame = ctk.CTkFrame(parent)
        concurrent_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        concurrent_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        concurrent_title = ctk.CTkLabel(
            concurrent_frame,
            text="⚡ 并发设置",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        concurrent_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(concurrent_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure(1, weight=1)

        # 并发数标签
        concurrent_label = ctk.CTkLabel(
            content_frame,
            text="并发上传数:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        concurrent_label.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 并发数值显示
        self.concurrent_value_label = ctk.CTkLabel(
            content_frame,
            text="3",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="#0078d4"
        )
        self.concurrent_value_label.grid(row=0, column=1, padx=15, pady=(15, 10), sticky="e")

        # 并发数滑块
        self.concurrent_slider = ctk.CTkSlider(
            content_frame,
            from_=1,
            to=5,
            number_of_steps=4,
            variable=self.concurrent_var,
            command=self._on_concurrent_change,
            height=20
        )
        self.concurrent_slider.grid(row=1, column=0, columnspan=2, padx=15, pady=(0, 10), sticky="ew")

        # 滑块标签
        slider_labels_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        slider_labels_frame.grid(row=2, column=0, columnspan=2, padx=15, pady=(0, 15), sticky="ew")
        slider_labels_frame.grid_columnconfigure((0, 1, 2, 3, 4), weight=1)

        for i in range(1, 6):
            label = ctk.CTkLabel(
                slider_labels_frame,
                text=str(i),
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            label.grid(row=0, column=i-1, sticky="ew")

        # 提示信息
        hint_label = ctk.CTkLabel(
            content_frame,
            text="建议设置2-3个并发，避免系统负载过高",
            text_color="gray",
            font=ctk.CTkFont(size=11)
        )
        hint_label.grid(row=3, column=0, columnspan=2, padx=15, pady=(0, 15))

    def _create_progress_section(self, parent, row):
        """创建上传进度显示区域"""
        # 区域框架
        progress_frame = ctk.CTkFrame(parent)
        progress_frame.grid(row=row, column=0, padx=10, pady=10, sticky="ew")
        progress_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        progress_title = ctk.CTkLabel(
            progress_frame,
            text="📈 上传进度",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        progress_title.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="w")

        # 内容框架
        content_frame = ctk.CTkFrame(progress_frame)
        content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="ew")
        content_frame.grid_columnconfigure(1, weight=1)

        # 当前任务状态
        current_label = ctk.CTkLabel(
            content_frame,
            text="当前任务:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        current_label.grid(row=0, column=0, padx=15, pady=(15, 5), sticky="w")

        self.current_task_label = ctk.CTkLabel(
            content_frame,
            text="无",
            text_color="gray",
            font=ctk.CTkFont(size=13)
        )
        self.current_task_label.grid(row=0, column=1, padx=15, pady=(15, 5), sticky="w")

        # 总体进度统计
        stats_label = ctk.CTkLabel(
            content_frame,
            text="统计信息:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        stats_label.grid(row=1, column=0, padx=15, pady=5, sticky="w")

        self.stats_label = ctk.CTkLabel(
            content_frame,
            text="总计: 0 | 完成: 0 | 失败: 0",
            text_color="gray",
            font=ctk.CTkFont(size=13)
        )
        self.stats_label.grid(row=1, column=1, padx=15, pady=5, sticky="w")

        # 进度条标签
        progress_bar_label = ctk.CTkLabel(
            content_frame,
            text="整体进度:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        progress_bar_label.grid(row=2, column=0, columnspan=2, padx=15, pady=(10, 5), sticky="w")

        # 进度条
        self.progress_bar = ctk.CTkProgressBar(content_frame, height=20)
        self.progress_bar.grid(row=3, column=0, columnspan=2, padx=15, pady=(0, 15), sticky="ew")
        self.progress_bar.set(0)

        # 进度百分比
        self.progress_percentage = ctk.CTkLabel(
            content_frame,
            text="0%",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="#0078d4"
        )
        self.progress_percentage.grid(row=4, column=0, columnspan=2, padx=15, pady=(0, 15))

    def _create_log_section(self, parent, row):
        """创建日志显示区域"""
        # 区域框架
        log_frame = ctk.CTkFrame(parent)
        log_frame.grid(row=row, column=0, padx=10, pady=10, sticky="nsew")
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(1, weight=1)

        # 标题框架
        log_title_frame = ctk.CTkFrame(log_frame, fg_color="transparent")
        log_title_frame.grid(row=0, column=0, padx=15, pady=(15, 10), sticky="ew")
        log_title_frame.grid_columnconfigure(0, weight=1)

        # 区域标题
        log_title = ctk.CTkLabel(
            log_title_frame,
            text="📋 操作日志",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        log_title.grid(row=0, column=0, sticky="w")

        # 清空日志按钮
        clear_log_btn = ctk.CTkButton(
            log_title_frame,
            text="🗑️ 清空",
            width=80,
            height=30,
            command=self._clear_log,
            font=ctk.CTkFont(size=12)
        )
        clear_log_btn.grid(row=0, column=1, sticky="e")

        # 日志显示区域
        log_content_frame = ctk.CTkFrame(log_frame)
        log_content_frame.grid(row=1, column=0, padx=15, pady=(0, 15), sticky="nsew")
        log_content_frame.grid_columnconfigure(0, weight=1)
        log_content_frame.grid_rowconfigure(0, weight=1)

        # 日志文本框
        self.log_text = ctk.CTkTextbox(
            log_content_frame,
            height=250,
            font=ctk.CTkFont(family="Consolas", size=11),
            state="disabled"
        )
        self.log_text.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")

        # 配置父框架的行权重
        parent.grid_rowconfigure(row, weight=1)



    def _select_files(self):
        """选择视频文件"""
        file_types = [
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v"),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(
            title="选择视频文件",
            filetypes=file_types
        )

        if files:
            self.selected_files = list(files)
            self._update_file_list()
            self.logger.info(f"选择了 {len(files)} 个视频文件")

    def _update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            filename = Path(file_path).name
            self.file_listbox.insert(tk.END, filename)

    def _test_connection(self):
        """测试Hub Studio连接并获取环境列表"""
        try:
            api_url = self.api_url_var.get().strip()
            if not api_url:
                messagebox.showerror("错误", "请输入API地址")
                return

            self.hubstudio_api = HubStudioAPI(api_url)

            if self.hubstudio_api.test_connection():
                self.connection_status.configure(text="● 已连接", text_color="green")
                self._log_message("✅ Hub Studio连接测试成功")
                self._update_status("正在获取环境列表...")

                # 获取环境列表
                self._load_and_check_environments()

                messagebox.showinfo("连接成功", "Hub Studio连接成功！已获取环境列表。")
            else:
                self.connection_status.configure(text="● 连接失败", text_color="red")
                self._log_message("❌ Hub Studio连接测试失败")
                self._update_status("Hub Studio连接失败")
                messagebox.showerror("连接失败", "连接测试失败，请检查API地址和Hub Studio是否运行")

        except Exception as e:
            self.connection_status.configure(text="● 连接失败", text_color="red")
            error_msg = f"❌ 连接测试异常: {str(e)}"
            self._log_message(error_msg)
            self._update_status("连接测试异常")
            messagebox.showerror("连接异常", error_msg)

    def _load_and_check_environments(self):
        """加载并检查环境列表"""
        try:
            # 从配置文件加载环境ID列表
            if not self.environment_ids:
                self._log_message("⚠️ 未配置环境ID列表，请在配置文件中添加")
                self.env_stats_label.configure(text="未配置环境ID列表")
                return

            # 检查环境可用性
            self._log_message(f"🔍 正在检查 {len(self.environment_ids)} 个环境的可用性...")
            self.available_environments = self.hubstudio_api.get_all_available_environments(self.environment_ids)

            # 更新环境列表显示
            self._update_environment_list()

            available_count = len([env for env in self.available_environments if env['available']])
            self._log_message(f"✅ 环境检查完成，找到 {available_count} 个可用环境")
            self._update_status(f"找到 {available_count} 个可用环境")

        except Exception as e:
            error_msg = f"❌ 获取环境列表失败: {str(e)}"
            self._log_message(error_msg)
            self._update_status("获取环境列表失败")

    def _update_environment_list(self):
        """更新环境列表显示"""
        # 清空列表
        self.environment_listbox.delete(0, tk.END)

        if not self.available_environments:
            self.env_stats_label.configure(text="未检测到环境")
            return

        # 添加环境到列表
        for i, env in enumerate(self.available_environments):
            status_icon = "🟢" if env['status'] == 0 else "🔴" if env['status'] == 3 else "🟡"
            available_text = "可用" if env['available'] else "不可用"
            display_text = f"{status_icon} {env['containerCode']} - {env['statusText']} ({available_text})"
            self.environment_listbox.insert(tk.END, display_text)

        # 更新统计信息
        total_count = len(self.available_environments)
        available_count = len([env for env in self.available_environments if env['available']])
        running_count = len([env for env in self.available_environments if env['status'] == 0])

        stats_text = f"总计: {total_count} | 可用: {available_count} | 运行中: {running_count}"
        self.env_stats_label.configure(text=stats_text)

    def _update_status(self, message):
        """更新状态栏"""
        if hasattr(self, 'status_label'):
            self.status_label.configure(text=message)

    def _clear_log(self):
        """清空日志"""
        self.log_text.configure(state="normal")
        self.log_text.delete("1.0", tk.END)
        self.log_text.configure(state="disabled")
        self._update_status("日志已清空")

    def _on_concurrent_change(self, value):
        """并发数变化回调"""
        concurrent_count = int(value)
        self.concurrent_value_label.configure(text=str(concurrent_count))

        # 如果上传管理器已创建，更新并发数
        if self.upload_manager:
            self.upload_manager.max_concurrent = concurrent_count

    def _start_upload(self):
        """开始上传"""
        try:
            # 验证输入
            if not self._validate_inputs():
                return

            # 创建上传管理器
            if not self.hubstudio_api:
                messagebox.showerror("错误", "请先测试Hub Studio连接")
                return

            self.upload_manager = UploadManager(
                self.hubstudio_api,
                max_concurrent=self.concurrent_var.get()
            )

            # 设置回调函数
            self.upload_manager.set_status_callback(self._on_task_status_change)
            self.upload_manager.set_progress_callback(self._on_task_progress_change)
            self.upload_manager.set_completion_callback(self._on_task_completion)

            # 添加上传任务
            title = self.title_var.get().strip()
            description = self.description_text.get("1.0", tk.END).strip()

            # 获取可用环境列表
            available_envs = [env['containerCode'] for env in self.available_environments if env['available']]
            if not available_envs:
                messagebox.showerror("错误", "没有可用的浏览器环境，请先测试连接并确保有可用环境")
                return

            for i, file_path in enumerate(self.selected_files):
                task_id = f"task_{i+1}_{int(time.time())}"
                file_title = f"{title} - {Path(file_path).stem}" if len(self.selected_files) > 1 else title

                # 按顺序分配环境ID（循环使用）
                env_index = i % len(available_envs)
                container_code = available_envs[env_index]

                self.upload_manager.add_task(
                    task_id=task_id,
                    video_path=file_path,
                    title=file_title,
                    description=description,
                    container_code=container_code
                )

                self._log_message(f"📝 任务 {i+1}: {Path(file_path).name} -> 环境 {container_code}")

            # 启动上传
            if self.upload_manager.start_upload():
                self.is_uploading = True
                self._update_button_states()
                self._log_message(f"开始批量上传 {len(self.selected_files)} 个视频")
            else:
                messagebox.showerror("错误", "启动上传失败")

        except Exception as e:
            error_msg = f"启动上传异常: {str(e)}"
            self._log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def _toggle_pause(self):
        """切换暂停/恢复状态"""
        if not self.upload_manager:
            return

        if self.pause_btn.cget("text") == "暂停":
            self.upload_manager.pause_upload()
            self.pause_btn.configure(text="恢复")
            self._log_message("上传已暂停")
        else:
            self.upload_manager.resume_upload()
            self.pause_btn.configure(text="暂停")
            self._log_message("上传已恢复")

    def _stop_upload(self):
        """停止上传"""
        if self.upload_manager:
            self.upload_manager.stop_upload()
            self.upload_manager = None

        self.is_uploading = False
        self._update_button_states()
        self._log_message("上传已停止")

    def _validate_inputs(self) -> bool:
        """验证输入参数"""
        if not self.selected_files:
            messagebox.showerror("错误", "请选择要上传的视频文件")
            return False

        if not self.title_var.get().strip():
            messagebox.showerror("错误", "请输入视频标题")
            return False

        if not self.available_environments:
            messagebox.showerror("错误", "请先测试Hub Studio连接并获取环境列表")
            return False

        available_envs = [env for env in self.available_environments if env['available']]
        if not available_envs:
            messagebox.showerror("错误", "没有可用的浏览器环境")
            return False

        return True

    def _update_button_states(self):
        """更新按钮状态"""
        if self.is_uploading:
            self.start_btn.configure(state="disabled")
            self.pause_btn.configure(state="normal")
            self.stop_btn.configure(state="normal")
        else:
            self.start_btn.configure(state="normal")
            self.pause_btn.configure(state="disabled", text="暂停")
            self.stop_btn.configure(state="disabled")

    def _on_task_status_change(self, task_id: str, status: TaskStatus):
        """任务状态变化回调"""
        self.root.after(0, lambda: self._update_current_task(task_id, status))

    def _on_task_progress_change(self, task_id: str, progress: float):
        """任务进度变化回调"""
        self.root.after(0, lambda: self._update_progress(progress))

    def _on_task_completion(self, task_id: str, success: bool):
        """任务完成回调"""
        status_text = "成功" if success else "失败"
        self.root.after(0, lambda: self._log_message(f"任务 {task_id} 上传{status_text}"))
        self.root.after(0, self._update_statistics)

    def _update_current_task(self, task_id: str, status: TaskStatus):
        """更新当前任务显示"""
        if status == TaskStatus.RUNNING:
            self.current_task_label.configure(text=task_id, text_color="blue")
        elif status == TaskStatus.COMPLETED:
            self.current_task_label.configure(text="已完成", text_color="green")
        elif status == TaskStatus.FAILED:
            self.current_task_label.configure(text="失败", text_color="red")

    def _update_progress(self, progress: float):
        """更新进度条"""
        self.progress_bar.set(progress / 100.0)
        self.progress_percentage.configure(text=f"{progress:.1f}%")

    def _update_statistics(self):
        """更新统计信息"""
        if not self.upload_manager:
            return

        stats = self.upload_manager.get_statistics()
        stats_text = f"总计: {stats['total']} | 完成: {stats['completed']} | 失败: {stats['failed']}"
        self.stats_label.configure(text=stats_text)

        # 检查是否所有任务都完成
        if stats['running'] == 0 and stats['pending'] == 0 and self.is_uploading:
            self.is_uploading = False
            self._update_button_states()
            self._log_message("所有上传任务已完成")

    def _log_message(self, message: str):
        """添加日志消息"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.configure(state="normal")
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.configure(state="disabled")



    # 配置文件功能已移除，使用硬编码配置

    def _on_closing(self):
        """窗口关闭事件处理"""
        try:
            # 停止上传
            if self.is_uploading and self.upload_manager:
                self.upload_manager.stop_upload()

            # 关闭窗口
            self.root.destroy()

        except Exception as e:
            self.logger.error(f"关闭窗口时出错: {str(e)}")
            self.root.destroy()

    def run(self):
        """运行主窗口"""
        self.logger.info("启动YouTube自动化上传工具")
        self._log_message("YouTube自动化上传工具已启动")
        self.root.mainloop()


def main():
    """主函数"""
    app = MainWindow()
    app.run()


if __name__ == "__main__":
    main()
