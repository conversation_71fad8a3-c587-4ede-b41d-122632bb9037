"""
现代化简约GUI界面
采用黑白配色方案，简洁高效的设计
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from typing import List, Dict, Any, Optional
from pathlib import Path
import threading
import time
import json
import pygame
import os

from ..api.hubstudio_api import HubStudioAPI
from ..uploader.upload_manager import UploadManager, TaskStatus
from ..utils.logger import setup_logger, get_logger


class ModernWindow:
    """现代化主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        # 设置主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # 初始化日志
        setup_logger()
        self.logger = get_logger("modern_window")
        
        # 初始化音频
        self._init_audio()
        
        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("YouTube自动化上传工具")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # 设置窗口图标和样式
        self.root.configure(fg_color="#000000")  # 纯黑背景
        
        # 初始化变量
        self.selected_files: List[str] = []
        self.hubstudio_api: Optional[HubStudioAPI] = None
        self.upload_manager: Optional[UploadManager] = None
        self.is_uploading = False
        self.available_environments: List[Dict[str, Any]] = []
        
        # 配置变量 - 硬编码默认值
        self.api_url_var = ctk.StringVar(value="http://127.0.0.1:6873")
        self.app_id_var = ctk.StringVar(value="20250727139893885373591993G")  # 默认APP ID
        self.app_secret_var = ctk.StringVar(value="MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgE")  # 默认APP Secret
        self.title_var = ctk.StringVar()
        self.description_var = ctk.StringVar()
        self.concurrent_var = ctk.IntVar(value=3)
        
        # 创建界面
        self._create_interface()

        # 播放启动音效
        self._play_startup_sound()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        self.logger.info("现代化界面初始化完成")
    
    def _init_audio(self):
        """安全初始化音频系统"""
        try:
            # 设置pygame不显示欢迎信息
            os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'

            # 尝试初始化音频，设置较短的超时
            import threading
            import time

            def init_pygame_audio():
                try:
                    pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
                    pygame.mixer.init()
                    return True
                except Exception:
                    return False

            # 在后台线程中初始化，避免阻塞GUI
            audio_init_result = [False]

            def audio_init_thread():
                audio_init_result[0] = init_pygame_audio()

            init_thread = threading.Thread(target=audio_init_thread, daemon=True)
            init_thread.start()
            init_thread.join(timeout=2.0)  # 最多等待2秒

            if audio_init_result[0]:
                self.audio_enabled = True
                self.logger.debug("音频系统初始化成功")
            else:
                self.audio_enabled = False
                self.logger.debug("音频系统初始化失败或超时，禁用音频功能")

        except Exception as e:
            self.logger.warning(f"音频初始化异常: {e}")
            self.audio_enabled = False
    
    def _play_startup_sound(self):
        """播放启动音效"""
        if not self.audio_enabled:
            return
        
        try:
            # 创建启动音效（简单的音调序列）
            threading.Thread(target=self._generate_startup_sound, daemon=True).start()
        except Exception as e:
            self.logger.warning(f"播放启动音效失败: {e}")
    
    def _generate_startup_sound(self):
        """生成启动音效"""
        try:
            import numpy as np
            
            # 生成简单的启动音效
            sample_rate = 22050
            duration = 0.8
            
            # 创建音调序列 (C-E-G-C)
            frequencies = [261.63, 329.63, 392.00, 523.25]
            note_duration = duration / len(frequencies)
            
            audio_data = np.array([], dtype=np.float32)
            
            for freq in frequencies:
                t = np.linspace(0, note_duration, int(sample_rate * note_duration))
                note = np.sin(2 * np.pi * freq * t) * 0.3
                # 添加淡入淡出效果
                fade_samples = int(sample_rate * 0.05)
                note[:fade_samples] *= np.linspace(0, 1, fade_samples)
                note[-fade_samples:] *= np.linspace(1, 0, fade_samples)
                audio_data = np.concatenate([audio_data, note])
            
            # 转换为pygame可用的格式
            audio_data = (audio_data * 32767).astype(np.int16)
            stereo_data = np.column_stack((audio_data, audio_data))
            
            # 播放音效
            sound = pygame.sndarray.make_sound(stereo_data)
            sound.play()
            
        except Exception as e:
            self.logger.warning(f"生成启动音效失败: {e}")
    
    def _create_interface(self):
        """创建界面"""
        # 配置网格权重
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=1)
        
        # 创建标题栏
        self._create_title_bar()
        
        # 创建主内容区域
        self._create_main_content()
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_title_bar(self):
        """创建标题栏"""
        title_frame = ctk.CTkFrame(
            self.root, 
            height=80, 
            corner_radius=0,
            fg_color="#1a1a1a",
            border_width=1,
            border_color="#333333"
        )
        title_frame.grid(row=0, column=0, sticky="ew", padx=0, pady=0)
        title_frame.grid_columnconfigure(1, weight=1)
        title_frame.grid_propagate(False)
        
        # 应用标题
        title_label = ctk.CTkLabel(
            title_frame,
            text="YouTube 自动化上传工具",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color="#ffffff"
        )
        title_label.grid(row=0, column=0, padx=30, pady=20, sticky="w")
        
        # 版本和状态
        version_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        version_frame.grid(row=0, column=1, padx=30, pady=20, sticky="e")
        
        version_label = ctk.CTkLabel(
            version_frame,
            text="v2.0.0",
            font=ctk.CTkFont(size=14, weight="bold"),  # 添加加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        )
        version_label.pack(side="top", anchor="e")
        
        self.status_indicator = ctk.CTkLabel(
            version_frame,
            text="● 就绪",
            font=ctk.CTkFont(size=12),
            text_color="#00ff00"
        )
        self.status_indicator.pack(side="top", anchor="e", pady=(5, 0))
    
    def _create_main_content(self):
        """创建主内容区域"""
        # 主内容框架
        main_frame = ctk.CTkFrame(
            self.root,
            fg_color="#000000",
            corner_radius=0
        )
        main_frame.grid(row=1, column=0, sticky="nsew", padx=0, pady=0)
        main_frame.grid_columnconfigure((0, 1, 2), weight=1)
        main_frame.grid_rowconfigure(0, weight=1)
        
        # 左侧：连接和配置
        self._create_left_panel(main_frame)
        
        # 中间：文件和控制
        self._create_center_panel(main_frame)
        
        # 右侧：状态和日志
        self._create_right_panel(main_frame)
    
    def _create_left_panel(self, parent):
        """创建左侧面板"""
        left_frame = ctk.CTkFrame(
            parent,
            fg_color="#0a0a0a",
            border_width=1,
            border_color="#333333"
        )
        left_frame.grid(row=0, column=0, padx=(10, 5), pady=10, sticky="nsew")
        left_frame.grid_columnconfigure(0, weight=1)
        
        # 面板标题
        title = ctk.CTkLabel(
            left_frame,
            text="连接配置",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="#ffffff"
        )
        title.pack(pady=(20, 15))
        
        # API配置
        api_frame = ctk.CTkFrame(left_frame, fg_color="#1a1a1a")
        api_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        ctk.CTkLabel(
            api_frame,
            text="API地址",
            font=ctk.CTkFont(size=13, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).pack(pady=(15, 5))
        
        self.api_entry = ctk.CTkEntry(
            api_frame,
            textvariable=self.api_url_var,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            fg_color="#000000",
            border_color="#555555",
            text_color="#ffffff"
        )
        self.api_entry.pack(fill="x", padx=15, pady=(0, 10))

        # APP ID配置
        ctk.CTkLabel(
            api_frame,
            text="APP ID (可选)",
            font=ctk.CTkFont(size=13, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).pack(pady=(5, 5))

        self.app_id_entry = ctk.CTkEntry(
            api_frame,
            textvariable=self.app_id_var,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            fg_color="#000000",
            border_color="#555555",
            text_color="#ffffff",
            placeholder_text="输入Hub Studio APP ID"
        )
        self.app_id_entry.pack(fill="x", padx=15, pady=(0, 10))

        # APP Secret配置
        ctk.CTkLabel(
            api_frame,
            text="APP Secret (可选)",
            font=ctk.CTkFont(size=13, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).pack(pady=(5, 5))

        self.app_secret_entry = ctk.CTkEntry(
            api_frame,
            textvariable=self.app_secret_var,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            fg_color="#000000",
            border_color="#555555",
            text_color="#ffffff",
            placeholder_text="输入Hub Studio APP Secret",
            show="*"  # 隐藏密钥显示
        )
        self.app_secret_entry.pack(fill="x", padx=15, pady=(0, 15))
        
        # 连接按钮
        self.connect_btn = ctk.CTkButton(
            left_frame,
            text="连接并获取环境",
            command=self._test_connection,
            height=40,
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color="#ffffff",
            text_color="#000000",
            hover_color="#cccccc"
        )
        self.connect_btn.pack(fill="x", padx=20, pady=(0, 15))
        
        # 环境列表
        env_frame = ctk.CTkFrame(left_frame, fg_color="#1a1a1a")
        env_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        ctk.CTkLabel(
            env_frame,
            text="浏览器环境",
            font=ctk.CTkFont(size=14, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).pack(pady=(15, 10))
        
        # 环境列表框
        self.env_listbox = tk.Listbox(
            env_frame,
            bg="#000000",
            fg="#ffffff",
            selectbackground="#333333",
            selectforeground="#ffffff",
            font=("Consolas", 11, "bold"),  # 增大字体并加粗
            relief="flat",
            borderwidth=0,
            height=8
        )
        self.env_listbox.pack(fill="both", expand=True, padx=15, pady=(0, 10))
        
        self.env_status_label = ctk.CTkLabel(
            env_frame,
            text="未连接",
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        )
        self.env_status_label.pack(pady=(0, 15))

    def _create_center_panel(self, parent):
        """创建中间面板"""
        center_frame = ctk.CTkFrame(
            parent,
            fg_color="#0a0a0a",
            border_width=1,
            border_color="#333333"
        )
        center_frame.grid(row=0, column=1, padx=5, pady=10, sticky="nsew")
        center_frame.grid_columnconfigure(0, weight=1)
        center_frame.grid_rowconfigure(1, weight=1)

        # 面板标题
        title = ctk.CTkLabel(
            center_frame,
            text="视频配置",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="#ffffff"
        )
        title.grid(row=0, column=0, pady=(20, 15))

        # 配置区域 - 使用滚动框架
        self.config_scroll = ctk.CTkScrollableFrame(
            center_frame,
            fg_color="#1a1a1a",
            scrollbar_button_color="#333333",
            scrollbar_button_hover_color="#555555"
        )
        self.config_scroll.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 15))
        self.config_scroll.grid_columnconfigure(0, weight=1)

        # 全局配置区域
        global_frame = ctk.CTkFrame(self.config_scroll, fg_color="#2a2a2a")
        global_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=(10, 5))
        global_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            global_frame,
            text="📋 全局配置",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#ffffff"
        ).grid(row=0, column=0, columnspan=2, pady=(15, 10))

        # 文件选择
        ctk.CTkLabel(
            global_frame,
            text="视频文件:",
            font=ctk.CTkFont(size=13, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).grid(row=1, column=0, padx=(15, 10), pady=(0, 10), sticky="w")

        file_btn_frame = ctk.CTkFrame(global_frame, fg_color="transparent")
        file_btn_frame.grid(row=1, column=1, padx=(0, 15), pady=(0, 10), sticky="ew")
        file_btn_frame.grid_columnconfigure(0, weight=1)

        self.select_files_btn = ctk.CTkButton(
            file_btn_frame,
            text="选择文件",
            command=self._select_files,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555"
        )
        self.select_files_btn.grid(row=0, column=0, sticky="ew")

        self.file_count_label = ctk.CTkLabel(
            file_btn_frame,
            text="未选择",
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        )
        self.file_count_label.grid(row=0, column=1, padx=(10, 0))

        # 并发设置
        ctk.CTkLabel(
            global_frame,
            text="并发数量:",
            font=ctk.CTkFont(size=13, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).grid(row=2, column=0, padx=(15, 10), pady=(0, 15), sticky="w")

        concurrent_frame = ctk.CTkFrame(global_frame, fg_color="transparent")
        concurrent_frame.grid(row=2, column=1, padx=(0, 15), pady=(0, 15), sticky="ew")
        concurrent_frame.grid_columnconfigure(0, weight=1)

        # 并发数量输入框和滑块组合
        concurrent_input_frame = ctk.CTkFrame(concurrent_frame, fg_color="transparent")
        concurrent_input_frame.grid(row=0, column=0, sticky="ew")
        concurrent_input_frame.grid_columnconfigure(0, weight=1)

        # 输入框
        self.concurrent_entry = ctk.CTkEntry(
            concurrent_input_frame,
            textvariable=self.concurrent_var,
            width=60,
            height=30,
            font=ctk.CTkFont(size=12, weight="bold"),
            fg_color="#000000",
            border_color="#555555",
            text_color="#ffffff",
            justify="center"
        )
        self.concurrent_entry.grid(row=0, column=0, padx=(0, 10))
        self.concurrent_entry.bind('<KeyRelease>', self._on_concurrent_entry_change)
        self.concurrent_entry.bind('<FocusOut>', self._on_concurrent_entry_change)

        # 滑块
        self.concurrent_slider = ctk.CTkSlider(
            concurrent_input_frame,
            from_=1,
            to=10,  # 增加到10个并发
            number_of_steps=9,
            variable=self.concurrent_var,
            command=self._on_concurrent_slider_change,
            height=20,
            fg_color="#333333",
            progress_color="#ffffff",
            button_color="#ffffff",
            button_hover_color="#cccccc"
        )
        self.concurrent_slider.grid(row=0, column=1, sticky="ew")

        # 显示标签
        self.concurrent_label = ctk.CTkLabel(
            concurrent_frame,
            text="3 环境",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="#ffffff"
        )
        self.concurrent_label.grid(row=0, column=1, padx=(10, 0))

        # 环境特定配置区域
        self.env_configs_frame = ctk.CTkFrame(self.config_scroll, fg_color="#2a2a2a")
        self.env_configs_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        self.env_configs_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            self.env_configs_frame,
            text="🌐 环境配置",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#ffffff"
        ).grid(row=0, column=0, pady=(15, 10))

        # 环境配置容器
        self.env_config_container = ctk.CTkFrame(self.env_configs_frame, fg_color="transparent")
        self.env_config_container.grid(row=1, column=0, sticky="ew", padx=15, pady=(0, 15))
        self.env_config_container.grid_columnconfigure(0, weight=1)

        # 存储环境配置的字典
        self.env_configs = {}

        # 控制按钮
        control_frame = ctk.CTkFrame(center_frame, fg_color="#1a1a1a")
        control_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=(0, 20))
        control_frame.grid_columnconfigure((0, 1, 2), weight=1)

        self.start_btn = ctk.CTkButton(
            control_frame,
            text="开始上传",
            command=self._start_upload,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="#ffffff",
            text_color="#000000",
            hover_color="#cccccc"
        )
        self.start_btn.grid(row=0, column=0, padx=10, pady=15, sticky="ew")

        self.pause_btn = ctk.CTkButton(
            control_frame,
            text="暂停",
            command=self._toggle_pause,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="#666666",
            text_color="#ffffff",
            hover_color="#888888",
            state="disabled"
        )
        self.pause_btn.grid(row=0, column=1, padx=10, pady=15, sticky="ew")

        self.stop_btn = ctk.CTkButton(
            control_frame,
            text="停止",
            command=self._stop_upload,
            height=45,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555",
            state="disabled"
        )
        self.stop_btn.grid(row=0, column=2, padx=10, pady=15, sticky="ew")

    def _create_right_panel(self, parent):
        """创建右侧面板"""
        right_frame = ctk.CTkFrame(
            parent,
            fg_color="#0a0a0a",
            border_width=1,
            border_color="#333333"
        )
        right_frame.grid(row=0, column=2, padx=(5, 10), pady=10, sticky="nsew")
        right_frame.grid_columnconfigure(0, weight=1)
        right_frame.grid_rowconfigure(2, weight=1)  # 让日志区域占据主要空间

        # 面板标题
        title = ctk.CTkLabel(
            right_frame,
            text="状态监控",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="#ffffff"
        )
        title.grid(row=0, column=0, pady=(20, 10))

        # 上传进度 - 紧凑显示在顶部
        progress_frame = ctk.CTkFrame(right_frame, fg_color="#1a1a1a")
        progress_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 10))

        self.progress_percentage = ctk.CTkLabel(
            progress_frame,
            text="0%",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color="#ffffff"
        )
        self.progress_percentage.pack(pady=15)

        # 操作日志区域 - 占据剩余所有空间，向上扩展
        log_frame = ctk.CTkFrame(right_frame, fg_color="#1a1a1a")
        log_frame.grid(row=2, column=0, sticky="nsew", padx=20, pady=(0, 20))
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(1, weight=1)

        log_title_frame = ctk.CTkFrame(log_frame, fg_color="transparent")
        log_title_frame.grid(row=0, column=0, sticky="ew", padx=15, pady=(10, 5))
        log_title_frame.grid_columnconfigure(0, weight=1)

        ctk.CTkLabel(
            log_title_frame,
            text="操作日志",
            font=ctk.CTkFont(size=14, weight="bold"),  # 增大字体
            text_color="#ffffff"  # 改为白色，提高对比度
        ).grid(row=0, column=0, sticky="w")

        clear_btn = ctk.CTkButton(
            log_title_frame,
            text="清空",
            command=self._clear_log,
            width=60,
            height=25,
            font=ctk.CTkFont(size=10),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555"
        )
        clear_btn.grid(row=0, column=1, sticky="e")

        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(family="Consolas", size=11, weight="bold"),  # 增大字体并加粗
            fg_color="#000000",
            border_color="#333333",
            text_color="#ffffff",  # 保持白色，确保高对比度
            state="disabled"
        )
        self.log_text.grid(row=1, column=0, sticky="nsew", padx=15, pady=(0, 10))

    def _create_status_bar(self):
        """创建状态栏"""
        status_frame = ctk.CTkFrame(
            self.root,
            height=30,
            corner_radius=0,
            fg_color="#1a1a1a",
            border_width=1,
            border_color="#333333"
        )
        status_frame.grid(row=2, column=0, sticky="ew", padx=0, pady=0)
        status_frame.grid_columnconfigure(1, weight=1)
        status_frame.grid_propagate(False)

        self.status_label = ctk.CTkLabel(
            status_frame,
            text="就绪",
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        )
        self.status_label.grid(row=0, column=0, padx=15, pady=5, sticky="w")

        # 时间显示
        time_label = ctk.CTkLabel(
            status_frame,
            text=time.strftime("%Y-%m-%d %H:%M:%S"),
            font=ctk.CTkFont(size=11, weight="bold"),  # 增大字体并加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        )
        time_label.grid(row=0, column=1, padx=15, pady=5, sticky="e")

    # 核心功能方法
    def _test_connection(self):
        """测试连接并获取环境"""
        try:
            api_url = self.api_url_var.get().strip()
            if not api_url:
                messagebox.showerror("错误", "请输入API地址")
                return

            self._update_status("正在连接...")
            self.connect_btn.configure(state="disabled", text="连接中...")

            # 在后台线程中执行连接
            threading.Thread(target=self._do_connection_test, args=(api_url,), daemon=True).start()

        except Exception as e:
            self._log_message(f"连接异常: {e}")
            self._update_status("连接失败")
            self.connect_btn.configure(state="normal", text="连接并获取环境")

    def _do_connection_test(self, api_url):
        """执行连接测试"""
        try:
            # 获取认证信息
            app_id = self.app_id_var.get().strip() or None
            app_secret = self.app_secret_var.get().strip() or None

            # 创建API实例
            self.hubstudio_api = HubStudioAPI(
                api_base_url=api_url,
                app_id=app_id,
                app_secret=app_secret
            )

            if self.hubstudio_api.test_connection():
                self._log_message("Hub Studio连接成功")

                # 在后台线程中加载环境，避免阻塞
                threading.Thread(target=self._load_and_check_environments_safe, daemon=True).start()

                # 更新UI
                self.root.after(0, lambda: [
                    self.connect_btn.configure(state="normal", text="重新连接"),
                    self._update_status("连接成功"),
                    self.status_indicator.configure(text="● 已连接", text_color="#00ff00")
                ])
            else:
                self._log_message("Hub Studio连接失败")
                self.root.after(0, lambda: [
                    self.connect_btn.configure(state="normal", text="连接并获取环境"),
                    self._update_status("连接失败"),
                    self.status_indicator.configure(text="● 连接失败", text_color="#ff0000")
                ])

        except Exception as e:
            self._log_message(f"连接异常: {e}")
            self.root.after(0, lambda: [
                self.connect_btn.configure(state="normal", text="连接并获取环境"),
                self._update_status("连接异常"),
                self.status_indicator.configure(text="● 连接异常", text_color="#ff0000")
            ])

    def _load_and_check_environments(self):
        """通过Hub Studio API动态获取并检查环境（同步版本，已弃用）"""
        # 这个方法已被_load_and_check_environments_safe替代
        self._load_and_check_environments_safe()

    def _load_and_check_environments_safe(self):
        """安全地通过Hub Studio API动态获取并检查环境"""
        try:
            self.root.after(0, lambda: self._log_message("正在从Hub Studio API获取环境列表..."))

            # 设置超时保护
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("API请求超时")

            # 在Windows上signal.alarm不可用，使用threading.Timer
            timeout_occurred = [False]

            def timeout_callback():
                timeout_occurred[0] = True

            timeout_timer = threading.Timer(10.0, timeout_callback)  # 10秒超时
            timeout_timer.start()

            try:
                # 通过API获取环境列表
                environments_data = self.hubstudio_api.get_environments_list()

                if timeout_occurred[0]:
                    raise TimeoutError("API请求超时")

                timeout_timer.cancel()  # 取消超时定时器

                if not environments_data:
                    self.root.after(0, lambda: self._log_message("未从API获取到环境数据"))
                    self.available_environments = []
                    self.root.after(0, self._update_environment_list)
                    return

                self.available_environments = []

                # 处理API返回的环境数据
                for env_data in environments_data:
                    try:
                        env_id = env_data.get('id', '')
                        env_name = env_data.get('name', env_id)
                        env_status = env_data.get('status', '未知')
                        create_time = env_data.get('createTime', '')
                        last_open_time = env_data.get('lastOpenTime', '')
                        proxy_type = env_data.get('proxyType', '')
                        last_ip = env_data.get('lastUsedIp', '')

                        # 根据环境数据确定可用性
                        available = bool(env_id and env_name)  # 有ID和名称就认为可用

                        env_info = {
                            'containerCode': env_id,
                            'status': 0 if available else -1,  # 0表示可用，-1表示不可用
                            'available': available,
                            'name': env_name,
                            'statusText': env_status,
                            'createTime': create_time,
                            'lastOpenTime': last_open_time,
                            'proxyType': proxy_type,
                            'lastUsedIp': last_ip
                        }
                        self.available_environments.append(env_info)

                        available_text = "可用" if available else "不可用"
                        status_display = f"{env_status} ({available_text})"
                        self.root.after(0, lambda msg=f"环境: {env_name} (ID: {env_id}) - {status_display}": self._log_message(msg))

                    except Exception as e:
                        self.root.after(0, lambda msg=f"处理环境数据失败: {e}": self._log_message(msg))
                        continue

                # 更新环境列表显示
                self.root.after(0, self._update_environment_list)

                available_count = len([env for env in self.available_environments if env['available']])
                total_count = len(self.available_environments)
                self.root.after(0, lambda: self._log_message(f"✅ 成功获取 {total_count} 个环境，其中 {available_count} 个可用"))

            except TimeoutError:
                timeout_timer.cancel()
                self.root.after(0, lambda: self._log_message("❌ 获取环境列表超时"))
                self.available_environments = []
                self.root.after(0, self._update_environment_list)

        except Exception as e:
            self.root.after(0, lambda: self._log_message(f"❌ 获取环境列表失败: {e}"))
            self.available_environments = []
            self.root.after(0, self._update_environment_list)

    def _update_environment_list(self):
        """更新环境列表显示"""
        self.env_listbox.delete(0, tk.END)

        if not self.available_environments:
            self.env_status_label.configure(text="未检测到环境")
            self._clear_env_configs()
            return

        for env in self.available_environments:
            status_icon = "●" if env['available'] else "○"
            env_name = env.get('name', '未命名')
            env_id = env.get('containerCode', '')
            status_text = env.get('statusText', '未知')
            available_text = "可用" if env['available'] else "不可用"

            # 格式：● 环境名称 (ID: 123456) [状态] 可用
            display_text = f"{status_icon} {env_name} (ID: {env_id}) [{status_text}] {available_text}"
            self.env_listbox.insert(tk.END, display_text)

        total_count = len(self.available_environments)
        available_count = len([env for env in self.available_environments if env['available']])
        running_count = len([env for env in self.available_environments if env['status'] == 0])

        self.env_status_label.configure(text=f"总计:{total_count} 可用:{available_count} 运行:{running_count}")

        # 更新环境配置界面
        self._update_env_configs()

    def _update_env_configs(self):
        """更新环境配置界面"""
        # 清除现有配置
        self._clear_env_configs()

        if not self.available_environments:
            return

        # 为每个环境创建配置区域
        for i, env in enumerate(self.available_environments):
            container_code = env['containerCode']
            self._create_env_config(container_code, i)

    def _clear_env_configs(self):
        """清除环境配置"""
        for widget in self.env_config_container.winfo_children():
            widget.destroy()
        self.env_configs.clear()

    def _create_env_config(self, container_code: str, index: int):
        """为特定环境创建配置区域"""
        env_frame = ctk.CTkFrame(self.env_config_container, fg_color="#1a1a1a", border_width=1, border_color="#444444")
        env_frame.grid(row=index, column=0, sticky="ew", pady=5)
        env_frame.grid_columnconfigure(1, weight=1)

        # 环境标题
        env_title = ctk.CTkLabel(
            env_frame,
            text=f"🖥️ {container_code}",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="#ffffff"
        )
        env_title.grid(row=0, column=0, columnspan=2, pady=(10, 5))

        # 标题配置
        title_frame = ctk.CTkFrame(env_frame, fg_color="transparent")
        title_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=10, pady=5)
        title_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            title_frame,
            text="📝 标题:",
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        ).grid(row=0, column=0, sticky="w")

        # 标题输入框，带左右箭头
        title_input_frame = ctk.CTkFrame(title_frame, fg_color="transparent")
        title_input_frame.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        title_input_frame.grid_columnconfigure(1, weight=1)

        left_btn = ctk.CTkButton(
            title_input_frame,
            text="◀",
            width=30,
            height=30,
            font=ctk.CTkFont(size=12),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555",
            command=lambda: self._navigate_env_config(container_code, "title", -1)
        )
        left_btn.grid(row=0, column=0)

        title_entry = ctk.CTkEntry(
            title_input_frame,
            height=30,
            font=ctk.CTkFont(size=11, weight="bold"),  # 增大字体并加粗
            fg_color="#000000",
            border_color="#555555",
            text_color="#ffffff",
            placeholder_text=f"{container_code}的视频标题"
        )
        title_entry.grid(row=0, column=1, sticky="ew", padx=2)

        right_btn = ctk.CTkButton(
            title_input_frame,
            text="▶",
            width=30,
            height=30,
            font=ctk.CTkFont(size=12),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555",
            command=lambda: self._navigate_env_config(container_code, "title", 1)
        )
        right_btn.grid(row=0, column=2)

        # 描述配置
        desc_frame = ctk.CTkFrame(env_frame, fg_color="transparent")
        desc_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=10, pady=(0, 10))
        desc_frame.grid_columnconfigure(1, weight=1)

        ctk.CTkLabel(
            desc_frame,
            text="📄 描述:",
            font=ctk.CTkFont(size=12, weight="bold"),  # 增大字体并加粗
            text_color="#ffffff"  # 改为白色，提高对比度
        ).grid(row=0, column=0, sticky="nw", pady=(5, 0))

        # 描述输入框，带左右箭头
        desc_input_frame = ctk.CTkFrame(desc_frame, fg_color="transparent")
        desc_input_frame.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        desc_input_frame.grid_columnconfigure(1, weight=1)

        desc_left_btn = ctk.CTkButton(
            desc_input_frame,
            text="◀",
            width=30,
            height=60,
            font=ctk.CTkFont(size=12),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555",
            command=lambda: self._navigate_env_config(container_code, "description", -1)
        )
        desc_left_btn.grid(row=0, column=0)

        desc_text = ctk.CTkTextbox(
            desc_input_frame,
            height=60,
            font=ctk.CTkFont(size=11, weight="bold"),  # 增大字体并加粗
            fg_color="#000000",
            border_color="#555555",
            text_color="#ffffff"
        )
        desc_text.grid(row=0, column=1, sticky="ew", padx=2)
        desc_text.insert("1.0", f"{container_code}的视频描述")

        desc_right_btn = ctk.CTkButton(
            desc_input_frame,
            text="▶",
            width=30,
            height=60,
            font=ctk.CTkFont(size=12),
            fg_color="#333333",
            text_color="#ffffff",
            hover_color="#555555",
            command=lambda: self._navigate_env_config(container_code, "description", 1)
        )
        desc_right_btn.grid(row=0, column=2)

        # 保存配置引用
        self.env_configs[container_code] = {
            'title_entry': title_entry,
            'desc_text': desc_text,
            'frame': env_frame
        }

    def _navigate_env_config(self, container_code: str, field_type: str, direction: int):
        """导航环境配置（左右箭头功能）"""
        # 这里可以实现模板切换或者预设内容切换
        if field_type == "title":
            current_text = self.env_configs[container_code]['title_entry'].get()
            # 可以添加预设标题模板
            templates = [
                f"{container_code} - 默认标题",
                f"{container_code} - 教程视频",
                f"{container_code} - 演示视频",
                f"{container_code} - 测试视频"
            ]
            # 简单的模板切换逻辑
            if current_text in templates:
                current_index = templates.index(current_text)
                new_index = (current_index + direction) % len(templates)
                self.env_configs[container_code]['title_entry'].delete(0, tk.END)
                self.env_configs[container_code]['title_entry'].insert(0, templates[new_index])
        elif field_type == "description":
            # 描述模板切换
            templates = [
                f"这是{container_code}环境的视频描述",
                f"由{container_code}环境自动上传的视频",
                f"{container_code}环境测试视频",
                f"{container_code}环境演示内容"
            ]
            current_text = self.env_configs[container_code]['desc_text'].get("1.0", tk.END).strip()
            if current_text in templates:
                current_index = templates.index(current_text)
                new_index = (current_index + direction) % len(templates)
                self.env_configs[container_code]['desc_text'].delete("1.0", tk.END)
                self.env_configs[container_code]['desc_text'].insert("1.0", templates[new_index])

    def _select_files(self):
        """选择文件"""
        file_types = [
            ("视频文件", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm *.m4v"),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(
            title="选择视频文件",
            filetypes=file_types
        )

        if files:
            self.selected_files = list(files)
            count = len(self.selected_files)
            self.file_count_label.configure(text=f"{count} 个文件")
            self._log_message(f"选择了 {count} 个视频文件")
        else:
            self.file_count_label.configure(text="未选择")

    def _on_concurrent_slider_change(self, value):
        """滑块并发数变化"""
        count = int(value)
        self._update_concurrent_display(count)
        if self.upload_manager:
            self.upload_manager.max_concurrent = count

    def _on_concurrent_entry_change(self, event=None):
        """输入框并发数变化"""
        try:
            value = self.concurrent_entry.get().strip()
            if value:
                count = int(value)
                # 限制范围
                count = max(1, min(10, count))
                self.concurrent_var.set(count)
                self._update_concurrent_display(count)
                if self.upload_manager:
                    self.upload_manager.max_concurrent = count
        except ValueError:
            # 输入无效时恢复到当前值
            self.concurrent_entry.delete(0, tk.END)
            self.concurrent_entry.insert(0, str(self.concurrent_var.get()))

    def _update_concurrent_display(self, count):
        """更新并发数显示"""
        self.concurrent_label.configure(text=f"{count} 环境")
        # 更新滑块位置（如果是从输入框触发的）
        if self.concurrent_slider.get() != count:
            self.concurrent_slider.set(count)

    def _start_upload(self):
        """开始上传"""
        try:
            if not self._validate_inputs():
                return

            # 创建上传管理器
            self.upload_manager = UploadManager(
                self.hubstudio_api,
                max_concurrent=self.concurrent_var.get()
            )

            # 设置回调
            self.upload_manager.set_status_callback(self._on_task_status_change)
            self.upload_manager.set_progress_callback(self._on_task_progress_change)
            self.upload_manager.set_completion_callback(self._on_task_completion)
            self.upload_manager.set_env_status_callback(self._on_env_status_change)

            # 设置可用容器并初始化并发环境
            available_envs = [env['containerCode'] for env in self.available_environments if env['available']]
            if len(available_envs) == 0:
                messagebox.showerror("错误", "没有可用的浏览器环境")
                return

            self.upload_manager.set_available_containers(available_envs)

            # 如果并发数大于1，初始化并发环境
            if self.concurrent_var.get() > 1:
                self._log_message(f"正在初始化 {self.concurrent_var.get()} 个并发环境...")
                self._update_status("初始化环境中...")

                if not self.upload_manager.initialize_concurrent_environments():
                    messagebox.showerror("错误", "并发环境初始化失败")
                    return

                self._log_message("✅ 并发环境初始化完成")
            else:
                self._log_message("使用单环境模式")

            # 添加任务
            available_envs = [env['containerCode'] for env in self.available_environments if env['available']]

            for i, file_path in enumerate(self.selected_files):
                task_id = f"task_{i+1}_{int(time.time())}"

                env_index = i % len(available_envs)
                container_code = available_envs[env_index]

                # 获取环境特定配置
                if container_code in self.env_configs:
                    env_title = self.env_configs[container_code]['title_entry'].get().strip()
                    env_description = self.env_configs[container_code]['desc_text'].get("1.0", tk.END).strip()

                    # 如果环境配置为空，使用默认值
                    if not env_title:
                        env_title = f"{container_code} - {Path(file_path).stem}"
                    if not env_description:
                        env_description = f"由{container_code}环境上传的视频"
                else:
                    # 如果没有环境配置，使用默认值
                    env_title = f"{container_code} - {Path(file_path).stem}"
                    env_description = f"由{container_code}环境上传的视频"

                self.upload_manager.add_task(
                    task_id=task_id,
                    video_path=file_path,
                    title=env_title,
                    description=env_description,
                    container_code=container_code
                )

                self._log_message(f"任务 {i+1}: {Path(file_path).name} -> {container_code}")
                self._log_message(f"  标题: {env_title}")
                self._log_message(f"  描述: {env_description[:50]}{'...' if len(env_description) > 50 else ''}")

            # 显示上传步骤信息
            self._show_upload_steps_info()

            # 启动上传
            if self.upload_manager.start_upload():
                self.is_uploading = True
                self._update_button_states()
                self._log_message(f"🚀 开始自动上传 {len(self.selected_files)} 个视频")
                self._update_status("上传中...")
                self.status_indicator.configure(text="● 上传中", text_color="#ffff00")
            else:
                messagebox.showerror("错误", "启动上传失败")

        except Exception as e:
            self._log_message(f"启动上传异常: {e}")
            messagebox.showerror("错误", str(e))

    def _toggle_pause(self):
        """暂停/恢复"""
        if not self.upload_manager:
            return

        if self.upload_manager.is_paused:
            self.upload_manager.resume_upload()
            self.pause_btn.configure(text="暂停")
            self._log_message("恢复上传")
            self._update_status("上传中...")
        else:
            self.upload_manager.pause_upload()
            self.pause_btn.configure(text="恢复")
            self._log_message("暂停上传")
            self._update_status("已暂停")

    def _stop_upload(self):
        """停止上传"""
        if not self.upload_manager:
            return

        self.upload_manager.stop_upload()
        self.is_uploading = False
        self._update_button_states()
        self._log_message("停止上传")
        self._update_status("已停止")
        self.status_indicator.configure(text="● 已停止", text_color="#ff0000")

    def _validate_inputs(self):
        """验证输入"""
        if not self.selected_files:
            messagebox.showerror("错误", "请选择视频文件")
            return False

        if not self.available_environments:
            messagebox.showerror("错误", "请先连接Hub Studio并获取环境")
            return False

        available_envs = [env for env in self.available_environments if env['available']]
        if not available_envs:
            messagebox.showerror("错误", "没有可用的浏览器环境")
            return False

        # 检查是否至少有一个环境配置了标题
        has_valid_config = False
        for env in available_envs:
            container_code = env['containerCode']
            if container_code in self.env_configs:
                title = self.env_configs[container_code]['title_entry'].get().strip()
                if title:
                    has_valid_config = True
                    break

        if not has_valid_config:
            # 如果没有配置标题，给出提示但不阻止上传（会使用默认标题）
            result = messagebox.askyesno(
                "提示",
                "检测到环境配置中没有设置标题，将使用默认标题格式。是否继续？"
            )
            if not result:
                return False

        return True

    def _update_button_states(self):
        """更新按钮状态"""
        if self.is_uploading:
            self.start_btn.configure(state="disabled")
            self.pause_btn.configure(state="normal")
            self.stop_btn.configure(state="normal")
        else:
            self.start_btn.configure(state="normal")
            self.pause_btn.configure(state="disabled", text="暂停")
            self.stop_btn.configure(state="disabled")

    # 回调方法
    def _on_task_status_change(self, task_id: str, status: TaskStatus):
        """任务状态变化（增强版）"""
        def update_ui():
            if status == TaskStatus.RUNNING:
                self._log_message(f"🚀 任务开始: {task_id}")
                self._update_status(f"正在上传: {task_id}")
                # 播放开始音效
                self._play_sound("start")
            elif status == TaskStatus.COMPLETED:
                self._log_message(f"✅ 任务完成: {task_id}")
                # 播放成功音效
                self._play_sound("success")
            elif status == TaskStatus.FAILED:
                self._log_message(f"❌ 任务失败: {task_id}")
                # 播放错误音效
                self._play_sound("error")
            elif status == TaskStatus.CANCELLED:
                self._log_message(f"⚠️ 任务取消: {task_id}")
            elif status == TaskStatus.PENDING:
                self._log_message(f"⏳ 任务等待: {task_id}")

        self.root.after(0, update_ui)

    def _on_task_progress_change(self, task_id: str, progress: float):
        """任务进度变化（增强版）"""
        def update_ui():
            # 更新进度条和百分比
            self.progress_bar.set(progress / 100.0)
            self.progress_percentage.configure(text=f"{progress:.1f}%")

            # 根据进度阶段显示不同的状态信息
            if progress <= 10:
                status_text = "🔍 查找上传按钮..."
            elif progress <= 30:
                status_text = "📤 上传视频文件..."
            elif progress <= 50:
                status_text = "📝 填写视频信息..."
            elif progress <= 60:
                status_text = "👶 设置儿童内容..."
            elif progress <= 90:
                status_text = "⏭️ 导航设置页面..."
            elif progress < 100:
                status_text = "🚀 发布视频..."
            else:
                status_text = "✅ 上传完成"

            self._update_status(f"{status_text} ({progress:.1f}%)")

            # 记录关键进度点
            key_points = [10, 30, 50, 60, 70, 80, 90, 100]
            if any(abs(progress - point) < 0.1 for point in key_points):
                self._log_message(f"📊 {task_id}: {status_text} {progress:.1f}%")

        self.root.after(0, update_ui)

    def _on_task_completion(self, task_id: str, success: bool):
        """任务完成（增强版）"""
        def update_ui():
            if success:
                self._log_message(f"🎉 任务成功完成: {task_id}")
                self._update_status("✅ 上传成功")
                # 播放成功音效
                self._play_sound("success")

                # 检查是否所有任务都完成
                if self.upload_manager:
                    all_tasks = self.upload_manager.get_all_tasks()
                    completed_tasks = [t for t in all_tasks.values() if t.status == TaskStatus.COMPLETED]
                    total_tasks = len(all_tasks)

                    if len(completed_tasks) == total_tasks:
                        self._log_message(f"🎊 所有任务完成！总计: {total_tasks} 个")
                        self._update_status(f"✅ 全部完成 ({total_tasks}/{total_tasks})")
                        self._play_sound("complete")
                    else:
                        self._update_status(f"✅ 进行中 ({len(completed_tasks)}/{total_tasks})")
            else:
                self._log_message(f"💥 任务执行失败: {task_id}")
                self._update_status("❌ 上传失败")
                # 播放错误音效
                self._play_sound("error")

                # 显示错误详情（如果有的话）
                if self.upload_manager:
                    all_tasks = self.upload_manager.get_all_tasks()
                    if task_id in all_tasks:
                        task = all_tasks[task_id]
                        if task.error_message:
                            self._log_message(f"❌ 错误详情: {task.error_message}")

        self.root.after(0, update_ui)

        # 检查是否所有任务完成
        if self.upload_manager:
            stats = self.upload_manager.get_statistics()
            if stats['running'] == 0 and stats['pending'] == 0:
                self.is_uploading = False
                self.root.after(0, lambda: [
                    self._update_button_states(),
                    self._update_status("上传完成"),
                    self.status_indicator.configure(text="● 完成", text_color="#00ff00")
                ])

    def _show_upload_steps_info(self):
        """显示上传步骤信息"""
        steps_info = [
            "🚀 YouTube自动上传9步流程：",
            "",
            "1️⃣ 点击上传按钮 (10%) - 智能查找，多语言支持",
            "2️⃣ 上传视频文件 (30%) - 自动选择，进度监控",
            "3️⃣ 填写标题描述 (50%) - 智能表单，多种方式",
            "4️⃣ 设置儿童内容 (60%) - 自动选择非儿童内容",
            "5️⃣ 点击下一步1 (70%) - 进入可见性设置",
            "6️⃣ 点击下一步2 (80%) - 进入检查页面",
            "7️⃣ 点击下一步3 (90%) - 进入发布设置",
            "8️⃣ 设置可见性 (95%) - 支持公开/私密/不公开",
            "9️⃣ 点击发布 (100%) - 完成发布，成功确认",
            "",
            "✨ 全程自动化，无需人工干预！"
        ]

        for line in steps_info:
            self._log_message(line)

    def _on_env_status_change(self, container_code: str, task_id: str, status: str):
        """环境状态变化回调"""
        status_map = {
            'idle': '🟢 空闲',
            'busy': '🔵 忙碌',
            'connecting': '🟡 连接中',
            'error': '🔴 错误',
            'disconnected': '⚫ 断开'
        }

        status_text = status_map.get(status, status)

        if task_id:
            self._log_message(f"环境 {container_code}: {status_text} (任务: {task_id})")
        else:
            self._log_message(f"环境 {container_code}: {status_text}")

    # 工具方法
    def _add_log_to_gui(self, message: str):
        """添加日志到GUI（内部方法）"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        def update_log():
            if hasattr(self, 'log_text'):
                self.log_text.configure(state="normal")
                self.log_text.insert(tk.END, log_entry)
                self.log_text.see(tk.END)
                self.log_text.configure(state="disabled")

        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)

    def _clear_log(self):
        """清空日志"""
        self.log_text.configure(state="normal")
        self.log_text.delete("1.0", tk.END)
        self.log_text.configure(state="disabled")

    def _update_status(self, message: str):
        """更新状态"""
        def update():
            self.status_label.configure(text=message)

        if threading.current_thread() == threading.main_thread():
            update()
        else:
            self.root.after(0, update)

    def _log_message(self, message: str):
        """记录日志消息（主方法）"""
        # 记录到日志文件
        self.logger.info(message)

        # 在界面中显示消息
        self._add_log_to_gui(message)

    def _on_closing(self):
        """强力关闭窗口，确保无阻塞"""
        import threading
        import time
        import os
        import signal

        # 设置关闭标志，防止重复关闭
        if hasattr(self, '_closing_in_progress'):
            return
        self._closing_in_progress = True

        def emergency_exit():
            """紧急退出，用于极端情况"""
            try:
                self.logger.critical("紧急退出程序")
                os._exit(1)  # 强制退出整个进程
            except:
                os._exit(1)

        def force_close():
            """强制关闭，1秒超时保护"""
            try:
                self.logger.warning("强制关闭应用程序")

                # 强制停止所有线程和资源
                if hasattr(self, 'upload_manager') and self.upload_manager:
                    try:
                        self.upload_manager.is_running = False
                        if hasattr(self.upload_manager, 'executor') and self.upload_manager.executor:
                            self.upload_manager.executor.shutdown(wait=False)
                    except:
                        pass

                # 强制关闭GUI
                try:
                    self.root.quit()
                    self.root.destroy()
                except:
                    pass

                # 如果还没退出，启动紧急退出
                threading.Timer(1.0, emergency_exit).start()

            except:
                emergency_exit()

        def instant_cleanup():
            """即时清理，不等待任何操作"""
            try:
                self.logger.info("开始即时关闭应用程序...")

                # 立即设置停止标志
                self.is_uploading = False

                # 不显示确认对话框，直接关闭
                # 在后台线程中进行资源清理，不阻塞GUI关闭
                def background_cleanup():
                    try:
                        if hasattr(self, 'upload_manager') and self.upload_manager:
                            self.logger.info("后台停止上传管理器...")
                            self.upload_manager.stop_upload_safe()
                    except Exception as e:
                        self.logger.warning(f"后台清理失败: {e}")

                # 启动后台清理（不等待完成）
                cleanup_thread = threading.Thread(target=background_cleanup, daemon=True)
                cleanup_thread.start()

                # 立即关闭GUI - 检查是否在主线程中
                self.logger.info("立即关闭GUI窗口")
                if threading.current_thread() == threading.main_thread():
                    try:
                        self.root.quit()
                    except:
                        pass
                    try:
                        self.root.destroy()
                    except:
                        pass
                else:
                    # 如果不在主线程中，使用after调度到主线程
                    try:
                        self.root.after(0, lambda: [self.root.quit(), self.root.destroy()])
                    except:
                        pass
                return True

            except Exception as e:
                self.logger.error(f"即时清理时出错: {e}")
                try:
                    if threading.current_thread() == threading.main_thread():
                        self.root.quit()
                        self.root.destroy()
                    else:
                        self.root.after(0, lambda: [self.root.quit(), self.root.destroy()])
                except:
                    pass
                return True

        try:
            # 启动1秒强制关闭保护
            force_timer = threading.Timer(1.0, force_close)
            force_timer.daemon = True
            force_timer.start()

            # 执行即时清理
            if instant_cleanup():
                force_timer.cancel()  # 取消强制关闭

        except Exception as e:
            self.logger.error(f"关闭窗口时出错: {e}")
            force_close()

    def run(self):
        """运行应用"""
        self._log_message("YouTube自动化上传工具启动")

        # 启动GUI响应性监控
        self._start_responsiveness_monitor()

        try:
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"GUI主循环异常: {e}")
            try:
                self.root.quit()
                self.root.destroy()
            except:
                pass

    def _start_responsiveness_monitor(self):
        """启动GUI响应性监控"""
        def monitor_responsiveness():
            """监控GUI响应性"""
            last_check = time.time()

            def check_response():
                nonlocal last_check
                current_time = time.time()
                if current_time - last_check > 5.0:  # 如果超过5秒没有响应
                    self.logger.warning("GUI响应缓慢，可能存在阻塞操作")
                last_check = current_time

                # 每秒检查一次
                self.root.after(1000, check_response)

            # 启动检查
            self.root.after(1000, check_response)

        # 在后台线程中启动监控
        threading.Thread(target=monitor_responsiveness, daemon=True).start()
