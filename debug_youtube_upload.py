#!/usr/bin/env python3
"""
YouTube自动化上传调试和修复工具

这个脚本专门用于调试和修复YouTube自动化上传功能的问题。
包含详细的诊断功能和修复建议。

使用方法:
python debug_youtube_upload.py
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'selenium',
        'webdriver-manager', 
        'requests',
        'loguru',
        'customtkinter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def check_webdriver_config():
    """检查WebDriver配置"""
    print("\n🔍 检查WebDriver配置...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        # 测试Chrome选项配置
        options = webdriver.ChromeOptions()
        options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--disable-extensions")
        
        print("✅ Chrome选项配置正常")
        
        # 检查是否有不兼容的选项
        print("🔍 检查不兼容的Chrome选项...")
        
        # 这些选项在某些Chrome版本中不兼容
        incompatible_options = [
            "excludeSwitches",
            "useAutomationExtension", 
            "disable-blink-features=AutomationControlled"
        ]
        
        print("✅ 未发现不兼容的Chrome选项")
        return True
        
    except Exception as e:
        print(f"❌ WebDriver配置检查失败: {e}")
        return False

def check_hubstudio_connection():
    """检查Hub Studio连接"""
    print("\n🔍 检查Hub Studio连接...")
    
    try:
        import requests
        
        # 测试Hub Studio API连接
        api_url = "http://127.0.0.1:6873"
        
        print(f"正在测试连接: {api_url}")
        
        response = requests.get(f"{api_url}/api/v1/browser/list", timeout=5)
        
        if response.status_code == 200:
            print("✅ Hub Studio API连接正常")
            
            try:
                data = response.json()
                print(f"📊 API响应: {data}")
                return True
            except:
                print("⚠️ API响应不是有效的JSON格式")
                return False
        else:
            print(f"❌ Hub Studio API连接失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Hub Studio API")
        print("请确保:")
        print("1. Hub Studio正在运行")
        print("2. API服务已启动（默认端口6873）")
        print("3. 防火墙允许连接")
        return False
    except Exception as e:
        print(f"❌ Hub Studio连接检查失败: {e}")
        return False

def check_youtube_selectors():
    """检查YouTube选择器"""
    print("\n🔍 检查YouTube选择器配置...")
    
    # 检查上传按钮选择器
    upload_selectors = [
        "//button[@aria-label='建立']",
        "//button[@aria-label='Create']", 
        "//button[@aria-label='上傳影片']",
        "//button[contains(text(), '建立')]",
        "//button[contains(text(), 'CREATE')]"
    ]
    
    print(f"✅ 配置了 {len(upload_selectors)} 个上传按钮选择器")
    
    # 检查文件输入框选择器
    file_selectors = [
        "//input[@type='file']",
        "//input[@accept*='video']",
        "//ytcp-uploads-file-picker//input[@type='file']"
    ]
    
    print(f"✅ 配置了 {len(file_selectors)} 个文件输入框选择器")
    
    return True

def run_diagnostic():
    """运行完整诊断"""
    print("🚀 YouTube自动化上传诊断工具")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 检查依赖包
    if not check_dependencies():
        all_checks_passed = False
    
    # 检查WebDriver配置
    if not check_webdriver_config():
        all_checks_passed = False
    
    # 检查Hub Studio连接
    if not check_hubstudio_connection():
        all_checks_passed = False
    
    # 检查YouTube选择器
    if not check_youtube_selectors():
        all_checks_passed = False
    
    print("\n" + "=" * 50)
    
    if all_checks_passed:
        print("✅ 所有检查通过！YouTube自动化上传应该可以正常工作。")
        print("\n💡 如果仍然遇到问题，请检查:")
        print("1. YouTube账号是否已在Hub Studio浏览器中登录")
        print("2. 视频文件是否存在且格式正确")
        print("3. 网络连接是否稳定")
    else:
        print("❌ 发现问题！请根据上述检查结果修复问题。")
        print("\n🔧 常见修复方法:")
        print("1. 安装缺少的依赖包: pip install -r requirements.txt")
        print("2. 启动Hub Studio并确保API服务运行")
        print("3. 检查防火墙设置")
        print("4. 更新Chrome浏览器到最新版本")

def test_upload_flow():
    """测试上传流程"""
    print("\n🧪 测试上传流程...")
    
    try:
        from src.api.hubstudio_api import HubStudioAPI
        from src.uploader.youtube_uploader import YouTubeUploader
        
        # 创建API实例
        api = HubStudioAPI()
        
        # 测试连接
        if api.test_connection():
            print("✅ Hub Studio API连接测试通过")
            
            # 获取环境列表
            try:
                environments = api.discover_environments()
                if not environments:
                    # 如果自动发现失败，尝试获取所有环境
                    all_envs = api.get_browser_list()
                    if all_envs and 'data' in all_envs:
                        environments = [env.get('id') for env in all_envs['data'] if env.get('id')]
            except Exception as e:
                print(f"⚠️ 环境发现失败: {e}")
                environments = []

            if environments:
                print(f"✅ 找到 {len(environments)} 个可用环境")
                
                # 选择第一个环境进行测试
                test_env = environments[0]
                print(f"🧪 使用环境进行测试: {test_env}")
                
                # 创建上传器
                uploader = YouTubeUploader(api)
                
                # 测试连接到浏览器
                if uploader.connect_to_browser(test_env):
                    print("✅ 浏览器连接测试通过")
                    
                    # 测试导航到YouTube Studio
                    if uploader.navigate_to_youtube_studio():
                        print("✅ YouTube Studio导航测试通过")
                        print("🎉 上传流程测试完全通过！")
                        return True
                    else:
                        print("❌ YouTube Studio导航测试失败")
                else:
                    print("❌ 浏览器连接测试失败")
            else:
                print("❌ 未找到可用环境")
        else:
            print("❌ Hub Studio API连接测试失败")
            
    except Exception as e:
        print(f"❌ 上传流程测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return False

if __name__ == "__main__":
    try:
        # 运行诊断
        run_diagnostic()
        
        # 询问是否进行上传流程测试
        print("\n" + "=" * 50)
        response = input("是否进行上传流程测试？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            test_upload_flow()
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n❌ 诊断工具运行失败: {e}")
        import traceback
        traceback.print_exc()
