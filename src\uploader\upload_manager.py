"""
批量上传管理器模块

提供YouTube视频的批量上传管理功能，支持任务队列、并发控制、
进度跟踪和错误处理机制。

主要功能：
- 任务队列管理和并发控制
- 上传进度跟踪和状态更新
- 错误重试和失败处理机制
- 任务持久化和恢复
"""

import json
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
from queue import Queue, Empty

from ..api.hubstudio_api import HubStudioAPI
from ..uploader.youtube_uploader import YouTubeUploader
from ..uploader.concurrent_env_manager import ConcurrentEnvironmentManager, EnvironmentStatus
from ..utils.logger import get_logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消


@dataclass
class UploadTask:
    """上传任务数据类"""
    id: str
    video_path: str
    title: str
    description: str
    visibility: str
    container_code: str
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    error_message: str = ""
    retry_count: int = 0
    created_at: float = 0.0
    started_at: float = 0.0
    completed_at: float = 0.0
    
    def __post_init__(self):
        if self.created_at == 0.0:
            self.created_at = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UploadTask':
        """从字典创建实例"""
        data['status'] = TaskStatus(data['status'])
        return cls(**data)


class UploadManager:
    """批量上传管理器"""
    
    def __init__(
        self,
        hubstudio_api: HubStudioAPI,
        max_concurrent: int = 3,
        max_retries: int = 3
    ):
        """
        初始化上传管理器

        Args:
            hubstudio_api: Hub Studio API实例
            max_concurrent: 最大并发数
            max_retries: 最大重试次数
        """
        self.hubstudio_api = hubstudio_api
        self.max_concurrent = max_concurrent
        self.max_retries = max_retries
        self.logger = get_logger("upload_manager")

        # 任务管理
        self.tasks: Dict[str, UploadTask] = {}
        self.task_queue: Queue = Queue()
        self.executor: Optional[ThreadPoolExecutor] = None
        self.running_tasks: Dict[str, Future] = {}

        # 状态管理
        self.is_running = False
        self.is_paused = False
        self._lock = threading.Lock()

        # 并发环境管理器
        self.env_manager: Optional[ConcurrentEnvironmentManager] = None
        self.available_containers: List[str] = []

        # 回调函数
        self.progress_callback: Optional[Callable[[str, float], None]] = None
        self.status_callback: Optional[Callable[[str, TaskStatus], None]] = None
        self.completion_callback: Optional[Callable[[str, bool], None]] = None
        self.env_status_callback: Optional[Callable[[str, str, str], None]] = None

        self.logger.info(f"上传管理器初始化完成 - 最大并发: {max_concurrent}")
    
    def add_task(
        self,
        task_id: str,
        video_path: str,
        title: str,
        description: str = "",
        visibility: str = "public",
        container_code: str = ""
    ) -> bool:
        """
        添加上传任务
        
        Args:
            task_id: 任务ID
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            visibility: 可见性
            container_code: Hub Studio环境ID
            
        Returns:
            是否添加成功
        """
        try:
            with self._lock:
                if task_id in self.tasks:
                    self.logger.warning(f"任务ID已存在: {task_id}")
                    return False
                
                # 验证视频文件
                if not Path(video_path).exists():
                    self.logger.error(f"视频文件不存在: {video_path}")
                    return False
                
                task = UploadTask(
                    id=task_id,
                    video_path=video_path,
                    title=title,
                    description=description,
                    visibility=visibility,
                    container_code=container_code
                )
                
                self.tasks[task_id] = task
                self.task_queue.put(task_id)
                
                self.logger.info(f"添加上传任务: {task_id} - {title}")

                return True
                
        except Exception as e:
            self.logger.error(f"添加任务失败: {str(e)}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否移除成功
        """
        try:
            with self._lock:
                if task_id not in self.tasks:
                    self.logger.warning(f"任务不存在: {task_id}")
                    return False
                
                task = self.tasks[task_id]
                
                # 如果任务正在运行，先取消
                if task.status == TaskStatus.RUNNING:
                    self.cancel_task(task_id)
                
                del self.tasks[task_id]
                self.logger.info(f"移除任务: {task_id}")
                self._save_tasks()
                
                return True
                
        except Exception as e:
            self.logger.error(f"移除任务失败: {str(e)}")
            return False
    
    def start_upload(self) -> bool:
        """
        开始批量上传
        
        Returns:
            是否启动成功
        """
        try:
            if self.is_running:
                self.logger.warning("上传管理器已在运行")
                return False
            
            self.is_running = True
            self.is_paused = False
            
            # 创建线程池
            self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent)
            
            # 启动任务调度线程
            threading.Thread(target=self._task_scheduler, daemon=True).start()
            
            self.logger.info("开始批量上传")
            return True
            
        except Exception as e:
            self.logger.error(f"启动上传失败: {str(e)}")
            self.is_running = False
            return False
    
    def pause_upload(self) -> None:
        """暂停上传"""
        self.is_paused = True
        self.logger.info("上传已暂停")
    
    def resume_upload(self) -> None:
        """恢复上传"""
        self.is_paused = False
        self.logger.info("上传已恢复")
    
    def stop_upload(self) -> None:
        """停止上传（可能阻塞）"""
        self.is_running = False
        self.is_paused = False

        # 取消所有运行中的任务
        with self._lock:
            for task_id in list(self.running_tasks.keys()):
                self.cancel_task(task_id)

        # 关闭线程池
        if self.executor:
            self.executor.shutdown(wait=True)
            self.executor = None

        self.logger.info("上传已停止")

    def stop_upload_safe(self) -> None:
        """安全停止上传，包含超时保护"""
        import threading
        import time

        def emergency_stop():
            """紧急停止，用于极端情况"""
            try:
                self.logger.critical("紧急停止上传管理器")
                self.is_running = False
                self.is_paused = False

                # 强制清理所有资源
                try:
                    if self.executor:
                        # 不等待，直接关闭
                        self.executor.shutdown(wait=False)
                        self.executor = None

                    # 清空任务列表
                    with self._lock:
                        self.running_tasks.clear()

                except:
                    pass

                self.logger.info("紧急停止完成")
            except:
                pass

        def force_stop():
            """强制停止，用于超时情况"""
            try:
                self.logger.warning("强制停止上传管理器")
                self.is_running = False
                self.is_paused = False

                # 强制关闭线程池
                if self.executor:
                    try:
                        # 立即关闭，不等待
                        self.executor.shutdown(wait=False)

                        # 强制终止所有线程
                        if hasattr(self.executor, '_threads'):
                            for thread in self.executor._threads:
                                if thread.is_alive():
                                    self.logger.warning(f"强制终止线程: {thread.name}")
                                    try:
                                        # 在Python中无法直接终止线程，但可以设置标志
                                        thread._stop()
                                    except:
                                        pass
                    except:
                        pass
                    finally:
                        self.executor = None

                # 清理运行任务
                try:
                    with self._lock:
                        self.running_tasks.clear()
                except:
                    pass

                self.logger.info("强制停止完成")
            except Exception as e:
                self.logger.error(f"强制停止时出错: {e}")

        try:
            self.logger.info("开始安全停止上传管理器...")

            # 立即设置停止标志
            self.is_running = False
            self.is_paused = False

            # 启动0.5秒紧急停止保护
            emergency_timer = threading.Timer(0.5, emergency_stop)
            emergency_timer.daemon = True
            emergency_timer.start()

            # 启动1秒强制停止保护
            force_timer = threading.Timer(1.0, force_stop)
            force_timer.daemon = True
            force_timer.start()

            # 快速取消任务（不等待）
            try:
                with self._lock:
                    for task_id in list(self.running_tasks.keys()):
                        try:
                            if task_id in self.running_tasks:
                                future = self.running_tasks[task_id]
                                future.cancel()
                                del self.running_tasks[task_id]
                        except:
                            pass
            except:
                pass

            # 快速关闭线程池
            if self.executor:
                try:
                    # 不等待，立即关闭
                    self.executor.shutdown(wait=False)
                    self.executor = None

                    # 取消定时器
                    emergency_timer.cancel()
                    force_timer.cancel()

                    self.logger.info("线程池快速关闭完成")

                except Exception as e:
                    self.logger.warning(f"快速关闭线程池时出错: {e}")
                    # 让定时器处理

            # 关闭环境管理器
            if self.env_manager:
                try:
                    self.env_manager.shutdown()
                    self.env_manager = None
                except Exception as e:
                    self.logger.warning(f"关闭环境管理器时出错: {e}")

            self.logger.info("上传管理器安全停止完成")

        except Exception as e:
            self.logger.error(f"安全停止时出错: {e}")
            emergency_stop()
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消指定任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否取消成功
        """
        try:
            with self._lock:
                if task_id not in self.tasks:
                    return False
                
                task = self.tasks[task_id]
                
                # 如果任务正在运行，取消Future
                if task_id in self.running_tasks:
                    future = self.running_tasks[task_id]
                    future.cancel()
                    del self.running_tasks[task_id]
                
                # 更新任务状态
                task.status = TaskStatus.CANCELLED
                self._notify_status_change(task_id, TaskStatus.CANCELLED)

                self.logger.info(f"任务已取消: {task_id}")
                # 注意：_save_tasks方法已移除，不再保存到文件

                return True
                
        except Exception as e:
            self.logger.error(f"取消任务失败: {str(e)}")
            return False

    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        return task.status if task else None

    def get_task_progress(self, task_id: str) -> float:
        """获取任务进度"""
        task = self.tasks.get(task_id)
        return task.progress if task else 0.0

    def get_all_tasks(self) -> List[UploadTask]:
        """获取所有任务"""
        return list(self.tasks.values())

    def get_statistics(self) -> Dict[str, int]:
        """获取统计信息"""
        stats = {
            "total": len(self.tasks),
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0,
            "cancelled": 0
        }

        for task in self.tasks.values():
            stats[task.status.value] += 1

        return stats

    def set_progress_callback(self, callback: Callable[[str, float], None]) -> None:
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable[[str, TaskStatus], None]) -> None:
        """设置状态回调函数"""
        self.status_callback = callback

    def set_completion_callback(self, callback: Callable[[str, bool], None]) -> None:
        """设置完成回调函数"""
        self.completion_callback = callback

    def set_env_status_callback(self, callback: Callable[[str, str, str], None]) -> None:
        """设置环境状态回调函数"""
        self.env_status_callback = callback

    def set_available_containers(self, container_codes: List[str]) -> None:
        """设置可用的容器代码列表"""
        self.available_containers = container_codes
        self.logger.info(f"设置可用容器: {container_codes}")

    def initialize_concurrent_environments(self) -> bool:
        """初始化并发环境"""
        if not self.available_containers:
            self.logger.error("没有可用的容器代码")
            return False

        # 创建环境管理器
        self.env_manager = ConcurrentEnvironmentManager(
            self.hubstudio_api,
            max_environments=min(self.max_concurrent, len(self.available_containers))
        )

        # 设置回调
        if self.env_status_callback:
            self.env_manager.set_status_callback(self._on_env_status_change)

        # 设置可用容器
        self.env_manager.set_available_containers(self.available_containers)

        # 初始化环境
        return self.env_manager.initialize_environments()

    def _on_env_status_change(self, container_code: str, task_id: str, status: EnvironmentStatus) -> None:
        """环境状态变化回调"""
        if self.env_status_callback:
            try:
                self.env_status_callback(container_code, task_id, status.value)
            except Exception as e:
                self.logger.warning(f"环境状态回调失败: {e}")

    def _task_scheduler(self) -> None:
        """任务调度器"""
        while self.is_running:
            try:
                if self.is_paused:
                    time.sleep(1)
                    continue

                # 检查是否有可用的执行槽位
                with self._lock:
                    if len(self.running_tasks) >= self.max_concurrent:
                        time.sleep(1)
                        continue

                # 从队列获取任务
                try:
                    task_id = self.task_queue.get(timeout=1)
                except Empty:
                    continue

                # 检查任务是否仍然有效
                with self._lock:
                    if task_id not in self.tasks:
                        continue

                    task = self.tasks[task_id]
                    if task.status != TaskStatus.PENDING:
                        continue

                    # 提交任务执行
                    future = self.executor.submit(self._execute_task, task_id)
                    self.running_tasks[task_id] = future

            except Exception as e:
                self.logger.error(f"任务调度器错误: {str(e)}")
                time.sleep(1)

    def _execute_task(self, task_id: str) -> None:
        """
        执行单个上传任务（使用环境管理器）

        Args:
            task_id: 任务ID
        """
        container_code = None
        uploader = None

        try:
            with self._lock:
                if task_id not in self.tasks:
                    return

                task = self.tasks[task_id]
                container_code = task.container_code
                task.status = TaskStatus.RUNNING
                task.started_at = time.time()
                self._notify_status_change(task_id, TaskStatus.RUNNING)

            self.logger.info(f"开始执行任务: {task_id}")

            # 如果使用环境管理器
            if self.env_manager:
                # 分配任务到环境
                if not self.env_manager.assign_task(container_code, task_id):
                    raise Exception(f"无法分配任务到环境 {container_code}")

                # 获取环境的上传器
                uploader = self.env_manager.get_environment_uploader(container_code)
                if not uploader:
                    raise Exception(f"环境 {container_code} 的上传器不可用")

                # 更新进度
                self._notify_progress_change(task_id, 20.0)
            else:
                # 传统模式：创建新的上传器
                uploader = YouTubeUploader(self.hubstudio_api)

                # 连接到浏览器环境
                if not uploader.connect_to_browser(container_code):
                    raise Exception("连接浏览器环境失败")

                # 导航到YouTube Studio
                if not uploader.navigate_to_youtube_studio():
                    raise Exception("导航到YouTube Studio失败")

                # 更新进度
                self._notify_progress_change(task_id, 20.0)

            # 执行视频上传（使用增强的自动化方法）
            with self._lock:
                task = self.tasks[task_id]  # 重新获取task对象

                # 定义进度回调函数
                def progress_callback(progress):
                    self._notify_progress_change(task_id, 20.0 + (progress * 0.8))  # 20-100%

                # 使用新的自动化上传方法
                success = uploader.upload_video_automated(
                    video_path=task.video_path,
                    title=task.title,
                    description=task.description,
                    visibility=task.visibility,
                    progress_callback=progress_callback
                )

            if success:
                # 上传成功
                with self._lock:
                    task = self.tasks[task_id]
                    task.status = TaskStatus.COMPLETED
                    task.progress = 100.0
                    task.completed_at = time.time()
                    self._notify_status_change(task_id, TaskStatus.COMPLETED)
                    self._notify_progress_change(task_id, 100.0)
                    self._notify_completion(task_id, True)

                self.logger.success(f"任务执行成功: {task_id}")
            else:
                raise Exception("视频上传失败")

        except Exception as e:
            # 处理任务失败
            self._handle_task_failure(task_id, str(e))

        finally:
            # 清理资源
            if container_code:
                if self.env_manager:
                    # 使用环境管理器时，释放环境
                    self.env_manager.release_environment(container_code)
                else:
                    # 传统模式：断开浏览器连接
                    if uploader:
                        try:
                            if not self.is_running:
                                uploader.emergency_disconnect(container_code)
                            else:
                                uploader.disconnect_browser_safe(container_code)
                        except Exception as e:
                            self.logger.warning(f"断开浏览器连接时出错: {str(e)}")

            # 从运行任务列表中移除
            with self._lock:
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]

    def _handle_task_failure(self, task_id: str, error_message: str) -> None:
        """
        处理任务失败

        Args:
            task_id: 任务ID
            error_message: 错误信息
        """
        with self._lock:
            if task_id not in self.tasks:
                return

            task = self.tasks[task_id]
            task.retry_count += 1
            task.error_message = error_message

            if task.retry_count <= self.max_retries:
                # 重新加入队列重试
                task.status = TaskStatus.PENDING
                self.task_queue.put(task_id)
                self.logger.warning(f"任务失败，将重试 ({task.retry_count}/{self.max_retries}): {task_id}")
                self._notify_status_change(task_id, TaskStatus.PENDING)
            else:
                # 达到最大重试次数，标记为失败
                task.status = TaskStatus.FAILED
                task.completed_at = time.time()
                self.logger.error(f"任务执行失败 (已达最大重试次数): {task_id} - {error_message}")
                self._notify_status_change(task_id, TaskStatus.FAILED)
                self._notify_completion(task_id, False)

    def _notify_progress_change(self, task_id: str, progress: float) -> None:
        """通知进度变化"""
        with self._lock:
            if task_id in self.tasks:
                self.tasks[task_id].progress = progress

        if self.progress_callback:
            try:
                self.progress_callback(task_id, progress)
            except Exception as e:
                self.logger.warning(f"进度回调函数执行失败: {str(e)}")

    def _notify_status_change(self, task_id: str, status: TaskStatus) -> None:
        """通知状态变化"""
        if self.status_callback:
            try:
                self.status_callback(task_id, status)
            except Exception as e:
                self.logger.warning(f"状态回调函数执行失败: {str(e)}")

    def _notify_completion(self, task_id: str, success: bool) -> None:
        """通知任务完成"""
        if self.completion_callback:
            try:
                self.completion_callback(task_id, success)
            except Exception as e:
                self.logger.warning(f"完成回调函数执行失败: {str(e)}")

    # 任务持久化功能已移除，不再保存到文件

    def __del__(self):
        """析构函数"""
        if self.is_running:
            self.stop_upload()
