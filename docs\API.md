# Hub Studio API集成说明

本文档详细说明了YouTube自动化上传工具与Hub Studio指纹浏览器的API集成方法。

## Hub Studio API概述

Hub Studio提供了完整的HTTP API接口，允许外部程序控制浏览器环境的启动、关闭和管理。

### API基础信息

- **基础URL**: `http://127.0.0.1:6873`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **认证方式**: 无需认证（本地API）

## 核心API接口

### 1. 启动浏览器环境

**接口**: `POST /api/v1/browser/start`

**请求参数**:
```json
{
  "containerCode": "环境ID",
  "isHeadless": false,
  "isWebDriverReadOnlyMode": false,
  "containerTabs": ["https://studio.youtube.com"],
  "args": ["--disable-blink-features=AutomationControlled"]
}
```

**响应示例**:
```json
{
  "msg": "Success",
  "code": 0,
  "data": {
    "browserID": "25633",
    "debuggingPort": 46973,
    "webdriver": "C:\\Users\\<USER>\\chromedriver.exe",
    "statusCode": 0
  }
}
```

**关键字段说明**:
- `debuggingPort`: WebDriver连接端口
- `webdriver`: ChromeDriver可执行文件路径
- `browserID`: 浏览器实例ID

### 2. 关闭浏览器环境

**接口**: `POST /api/v1/browser/stop`

**请求参数**:
```json
{
  "containerCode": "环境ID"
}
```

### 3. 查询浏览器状态

**接口**: `POST /api/v1/browser/all-browser-status`

**请求参数**:
```json
{
  "containerCodes": ["环境ID1", "环境ID2"]
}
```

**状态码说明**:
- `0`: 已开启
- `1`: 开启中
- `2`: 关闭中
- `3`: 已关闭

## 错误码参考

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 5 | 初始化代理失败 |
| 7 | 启动内核失败 |
| 17 | 容器正在被占用 |
| 20 | 不可打开状态 |
| 21 | 获取IP超时 |
| -10003 | 登录失败 |
| -10004 | 未找到环境信息 |
| -10008 | 系统资源不足 |
| -10013 | 环境正在运行 |

## Selenium WebDriver集成

### 连接到Hub Studio浏览器

```python
from selenium import webdriver
from selenium.webdriver.chrome.service import Service

def create_driver(webdriver_path: str, debugging_port: int):
    options = webdriver.ChromeOptions()
    options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debugging_port}")

    # 基本稳定性选项 - 使用兼容的选项
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-extensions")
    
    service = Service(webdriver_path)
    driver = webdriver.Chrome(service=service, options=options)
    
    # 执行脚本隐藏webdriver属性
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver
```

### 使用示例

```python
import requests
from selenium import webdriver

# 1. 启动浏览器环境
api_url = "http://127.0.0.1:6873"
start_data = {
    "containerCode": "your_environment_id",
    "isHeadless": False
}

response = requests.post(f"{api_url}/api/v1/browser/start", json=start_data)
result = response.json()

if result['code'] == 0:
    # 2. 获取连接信息
    webdriver_path = result['data']['webdriver']
    debugging_port = result['data']['debuggingPort']
    
    # 3. 创建WebDriver连接
    driver = create_driver(webdriver_path, debugging_port)
    
    # 4. 执行自动化操作
    driver.get("https://studio.youtube.com")
    
    # 5. 清理资源
    driver.quit()
    
    # 6. 关闭浏览器环境
    stop_data = {"containerCode": "your_environment_id"}
    requests.post(f"{api_url}/api/v1/browser/stop", json=stop_data)
```

## 最佳实践

### 1. 错误处理

```python
def safe_api_call(url, data, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, timeout=30)
            result = response.json()
            
            if result['code'] == 0:
                return result
            else:
                print(f"API错误: {result.get('msg', '未知错误')}")
                
        except requests.exceptions.RequestException as e:
            print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2)
    
    return None
```

### 2. 资源管理

```python
class HubStudioManager:
    def __init__(self, api_url, container_code):
        self.api_url = api_url
        self.container_code = container_code
        self.driver = None
    
    def __enter__(self):
        # 启动浏览器环境
        result = self.start_browser()
        if result:
            self.driver = self.create_driver(result)
        return self.driver
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # 清理资源
        if self.driver:
            self.driver.quit()
        self.stop_browser()
```

### 3. 并发控制

```python
import threading
from concurrent.futures import ThreadPoolExecutor

class ConcurrentUploader:
    def __init__(self, max_workers=3):
        self.max_workers = max_workers
        self.active_browsers = {}
        self.lock = threading.Lock()
    
    def upload_video(self, container_code, video_info):
        with self.lock:
            if len(self.active_browsers) >= self.max_workers:
                return False
            
            self.active_browsers[container_code] = True
        
        try:
            # 执行上传逻辑
            return self._do_upload(container_code, video_info)
        finally:
            with self.lock:
                self.active_browsers.pop(container_code, None)
```

## 注意事项

### 1. 环境准备
- 确保Hub Studio客户端已启动
- 环境ID必须存在且可用
- 浏览器环境需要预先配置YouTube账号登录

### 2. 性能优化
- 合理设置并发数量（建议不超过5个）
- 使用连接池复用HTTP连接
- 及时释放浏览器资源

### 3. 安全考虑
- API仅限本地访问
- 不要在公网暴露Hub Studio API端口
- 定期更新Hub Studio客户端版本

### 4. 故障恢复
- 实现重试机制
- 监控浏览器环境状态
- 处理网络中断和超时

## 调试技巧

### 1. 启用详细日志

```python
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 记录API调用
logger.debug(f"调用API: {url}, 参数: {data}")
```

### 2. 检查浏览器状态

```python
def check_browser_health(api_url, container_code):
    status_data = {"containerCodes": [container_code]}
    response = requests.post(f"{api_url}/api/v1/browser/all-browser-status", json=status_data)
    
    if response.status_code == 200:
        result = response.json()
        containers = result.get('data', {}).get('containers', [])
        for container in containers:
            if container.get('containerCode') == container_code:
                return container.get('status')
    
    return None
```

### 3. WebDriver调试

```python
# 启用Chrome DevTools
options.add_argument("--remote-debugging-port=9222")

# 保存页面截图
driver.save_screenshot("debug_screenshot.png")

# 获取页面源码
with open("debug_page_source.html", "w", encoding="utf-8") as f:
    f.write(driver.page_source)
```

## 相关资源

- [Hub Studio官方文档](https://support.hubstudio.cn/)
- [Selenium WebDriver文档](https://selenium-python.readthedocs.io/)
- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)

---

更多技术细节和问题解答，请参考项目的故障排除文档。
