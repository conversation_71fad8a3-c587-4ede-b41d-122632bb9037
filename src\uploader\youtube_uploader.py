"""
YouTube上传核心模块

实现YouTube Studio的自动化操作，包括视频上传、信息填写等功能。
支持与Hub Studio浏览器的集成，提供完整的错误处理和重试机制。

主要功能：
- Selenium WebDriver连接到Hub Studio浏览器
- YouTube Studio自动化操作流程
- 视频文件上传、标题描述填写、可见性设置
- 多种元素定位策略和容错机制
"""

import os
import time
from pathlib import Path
from typing import Optional, Dict, Any, List, Callable
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    WebDriverException,
    ElementNotInteractableException
)

from ..api.hubstudio_api import HubStudioAPI, HubStudioAPIError
from ..utils.logger import get_logger


class YouTubeUploaderError(Exception):
    """YouTube上传器异常类"""
    pass


class YouTubeUploader:
    """YouTube视频上传器"""
    
    # YouTube Studio URL
    YOUTUBE_STUDIO_URL = "https://studio.youtube.com"
    
    # 支持的视频格式
    SUPPORTED_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
    
    # 元素定位器 - 支持多种选择器策略
    SELECTORS = {
        'create_button': [
            "//tp-yt-iron-icon[@icon='yt-icons:add']",
            "//button[@aria-label='创建']",
            "//button[contains(@aria-label, 'Create')]",
            "//ytcp-button[contains(@class, 'create-button')]"
        ],
        'upload_video': [
            "//tp-yt-paper-item[contains(text(), '上传视频')]",
            "//tp-yt-paper-item[contains(text(), 'Upload video')]",
            "//div[contains(text(), '上传视频')]",
            "//div[contains(text(), 'Upload video')]"
        ],
        'file_input': [
            "//input[@type='file']",
            "//input[@accept]",
            "//ytcp-uploads-file-picker//input"
        ],
        'title_input': [
            "//div[@id='textbox' and @aria-label='标题']",
            "//div[@id='textbox' and contains(@aria-label, 'Title')]",
            "//ytcp-social-suggestions-textbox[@label='标题']//div[@id='textbox']",
            "//ytcp-social-suggestions-textbox[contains(@label, 'Title')]//div[@id='textbox']"
        ],
        'description_input': [
            "//div[@id='textbox' and @aria-label='描述']",
            "//div[@id='textbox' and contains(@aria-label, 'Description')]",
            "//ytcp-social-suggestions-textbox[@label='描述']//div[@id='textbox']",
            "//ytcp-social-suggestions-textbox[contains(@label, 'Description')]//div[@id='textbox']"
        ],
        'not_for_kids': [
            "//tp-yt-paper-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
            "//paper-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
            "//ytcp-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']"
        ],
        'next_button': [
            "//ytcp-button[@id='next-button']",
            "//button[@aria-label='下一步']",
            "//button[contains(@aria-label, 'Next')]",
            "//div[contains(text(), '下一步')]/.."
        ],
        'public_radio': [
            "//tp-yt-paper-radio-button[@name='PUBLIC']",
            "//paper-radio-button[@name='PUBLIC']",
            "//ytcp-radio-button[@name='PUBLIC']"
        ],
        'publish_button': [
            "//ytcp-button[@id='done-button']",
            "//button[@aria-label='发布']",
            "//button[contains(@aria-label, 'Publish')]",
            "//div[contains(text(), '发布')]/.."
        ]
    }
    
    def __init__(self, hubstudio_api: HubStudioAPI, wait_timeout: int = 20):
        """
        初始化YouTube上传器
        
        Args:
            hubstudio_api: Hub Studio API实例
            wait_timeout: 元素等待超时时间（秒）
        """
        self.hubstudio_api = hubstudio_api
        self.wait_timeout = wait_timeout
        self.logger = get_logger("youtube_uploader")
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        
    def _create_driver(self, webdriver_path: str, debugging_port: int) -> webdriver.Chrome:
        """
        创建Chrome WebDriver实例，使用稳定的配置

        Args:
            webdriver_path: WebDriver可执行文件路径
            debugging_port: 调试端口

        Returns:
            Chrome WebDriver实例
        """
        driver = None
        max_retries = 3

        for attempt in range(max_retries):
            try:
                self.logger.info(f"创建WebDriver连接 (尝试 {attempt + 1}/{max_retries})")
                self.logger.debug(f"WebDriver路径: {webdriver_path}")
                self.logger.debug(f"调试地址: 127.0.0.1:{debugging_port}")

                # 使用最简化、最兼容的Chrome选项配置
                options = webdriver.ChromeOptions()

                # 核心连接选项 - 连接到Hub Studio浏览器
                options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debugging_port}")

                # 基本稳定性选项 - 只使用最必要和兼容的选项
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-gpu")  # 禁用GPU加速，提高稳定性
                options.add_argument("--disable-extensions")  # 禁用扩展

                # 网络和安全相关 - 确保YouTube访问正常
                options.add_argument("--disable-web-security")
                options.add_argument("--allow-running-insecure-content")

                # 日志和调试 - 减少不必要的日志输出
                options.add_argument("--disable-logging")
                options.add_argument("--log-level=3")  # 只显示致命错误

                # 性能优化 - 提高响应速度
                options.add_argument("--disable-background-timer-throttling")
                options.add_argument("--disable-renderer-backgrounding")
                options.add_argument("--disable-backgrounding-occluded-windows")

                # 创建服务
                service = Service(webdriver_path)
                if hasattr(service, 'creation_flags'):
                    service.creation_flags = 0x08000000  # CREATE_NO_WINDOW flag for Windows

                self.logger.debug("正在初始化WebDriver...")
                driver = webdriver.Chrome(service=service, options=options)

                # 设置合理的超时时间
                driver.implicitly_wait(3)  # 减少隐式等待时间
                driver.set_page_load_timeout(45)  # 减少页面加载超时
                driver.set_script_timeout(20)  # 减少脚本超时

                # 测试连接稳定性
                self.logger.debug("测试WebDriver连接...")
                try:
                    current_url = driver.current_url
                    page_title = driver.title

                    # 验证连接是否有效
                    if not current_url or current_url == "data:,":
                        raise Exception("WebDriver连接无效，URL为空")

                    self.logger.info(f"✅ WebDriver连接成功!")
                    self.logger.debug(f"当前URL: {current_url}")
                    self.logger.debug(f"页面标题: {page_title}")

                    return driver

                except Exception as test_error:
                    self.logger.warning(f"WebDriver连接测试失败: {test_error}")
                    raise test_error

            except Exception as e:
                self.logger.warning(f"WebDriver创建失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                # 清理失败的driver
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = None

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    import time
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    # 最后一次尝试失败，抛出异常
                    self.logger.error(f"WebDriver创建失败，已尝试 {max_retries} 次")
                    raise
    
    def connect_to_browser(self, container_code: str) -> bool:
        """
        连接到Hub Studio浏览器环境，使用增强的稳定性机制

        Args:
            container_code: 环境ID

        Returns:
            是否连接成功
        """
        browser_started = False
        max_connection_retries = 2

        for connection_attempt in range(max_connection_retries):
            try:
                self.logger.info(f"连接到浏览器环境: {container_code} (尝试 {connection_attempt + 1}/{max_connection_retries})")

                # 如果不是第一次尝试，先清理之前的状态
                if connection_attempt > 0:
                    self._cleanup_driver()
                    if browser_started:
                        try:
                            self.hubstudio_api.stop_browser(container_code)
                            browser_started = False
                            import time
                            time.sleep(3)  # 等待浏览器完全关闭
                        except:
                            pass

                # 启动浏览器环境
                self.logger.info("启动Hub Studio浏览器环境...")
                browser_data = self.hubstudio_api.start_browser(container_code)
                browser_started = True

                webdriver_path = browser_data.get('webdriver')
                debugging_port = browser_data.get('debuggingPort')

                if not webdriver_path or not debugging_port:
                    raise YouTubeUploaderError("未获取到WebDriver路径或调试端口")

                self.logger.info(f"浏览器启动成功 - WebDriver: {webdriver_path}, 端口: {debugging_port}")

                # 等待浏览器准备就绪
                self.logger.info("等待浏览器环境准备就绪...")
                if not self.hubstudio_api.wait_for_browser_ready(container_code, 90):
                    raise YouTubeUploaderError("浏览器环境启动超时")

                self.logger.info("浏览器环境准备就绪，开始创建WebDriver连接...")

                # 创建WebDriver连接
                self.driver = self._create_driver(webdriver_path, debugging_port)
                self.wait = WebDriverWait(self.driver, self.wait_timeout)

                # 验证连接稳定性
                self.logger.info("验证WebDriver连接稳定性...")
                self._verify_driver_stability()

                self.logger.success(f"成功连接到浏览器环境: {container_code}")
                return True

            except Exception as e:
                self.logger.warning(f"连接尝试 {connection_attempt + 1} 失败: {str(e)}")

                # 清理失败的连接
                self._cleanup_driver()

                # 如果是最后一次尝试，进行最终清理
                if connection_attempt == max_connection_retries - 1:
                    if browser_started:
                        try:
                            self.logger.info(f"清理失败的浏览器环境: {container_code}")
                            self.hubstudio_api.stop_browser(container_code)
                        except Exception as cleanup_error:
                            self.logger.warning(f"清理浏览器环境时出错: {cleanup_error}")

                    self.logger.error(f"连接浏览器环境失败，已尝试 {max_connection_retries} 次")
                    return False

                # 等待后重试
                import time
                wait_time = (connection_attempt + 1) * 3
                self.logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

        return False

    def _cleanup_driver(self):
        """清理WebDriver资源"""
        try:
            if hasattr(self, 'driver') and self.driver:
                self.driver.quit()
        except:
            pass
        finally:
            self.driver = None
            self.wait = None

    def _verify_driver_stability(self):
        """验证WebDriver连接稳定性"""
        try:
            # 测试基本操作
            current_url = self.driver.current_url
            page_title = self.driver.title
            window_handles = self.driver.window_handles

            self.logger.debug(f"连接验证成功 - URL: {current_url}, 标题: {page_title}, 窗口数: {len(window_handles)}")

            # 测试JavaScript执行
            ready_state = self.driver.execute_script("return document.readyState")
            self.logger.debug(f"页面状态: {ready_state}")

        except Exception as e:
            raise YouTubeUploaderError(f"WebDriver连接不稳定: {e}")
    
    def disconnect_browser(self, container_code: str) -> None:
        """
        安全地断开浏览器连接，包含超时保护

        Args:
            container_code: 环境ID
        """
        import threading
        import time

        def force_disconnect():
            """强制断开连接"""
            try:
                self.logger.warning(f"强制断开浏览器连接: {container_code}")
                self._cleanup_driver()
            except:
                pass

        try:
            self.logger.info(f"开始断开浏览器连接: {container_code}")

            # 启动超时保护
            timeout_thread = threading.Timer(5.0, force_disconnect)
            timeout_thread.daemon = True
            timeout_thread.start()

            # 首先清理WebDriver
            self._cleanup_driver()

            # 然后关闭Hub Studio浏览器环境
            try:
                self.hubstudio_api.stop_browser(container_code)
                self.logger.info(f"Hub Studio浏览器环境已关闭: {container_code}")
            except Exception as hub_error:
                self.logger.warning(f"关闭Hub Studio浏览器环境时出错: {hub_error}")

            # 取消超时保护
            timeout_thread.cancel()
            self.logger.success(f"浏览器连接断开完成: {container_code}")

        except Exception as e:
            self.logger.warning(f"断开浏览器连接时出错: {str(e)}")
            force_disconnect()

    def disconnect_browser_safe(self, container_code: str) -> None:
        """
        快速安全断开浏览器连接，用于程序关闭时

        Args:
            container_code: 环境ID
        """
        try:
            self.logger.info(f"快速断开浏览器连接: {container_code}")

            # 立即清理WebDriver（不等待任何操作完成）
            try:
                if hasattr(self, 'driver') and self.driver:
                    # 尝试立即停止所有正在进行的操作
                    try:
                        self.driver.execute_script("window.stop();")  # 停止页面加载
                    except:
                        pass

                    # 强制关闭WebDriver
                    try:
                        self.driver.quit()
                    except:
                        # 如果quit()失败，尝试强制关闭
                        try:
                            self.driver.close()
                        except:
                            pass
            except:
                pass
            finally:
                self.driver = None
                self.wait = None

            # 在后台线程中关闭Hub Studio浏览器环境（完全不等待）
            try:
                import threading
                def close_browser():
                    try:
                        # 设置很短的超时
                        import requests
                        old_timeout = self.hubstudio_api.timeout
                        self.hubstudio_api.timeout = 1  # 1秒超时

                        self.hubstudio_api.stop_browser(container_code)

                        # 恢复原超时设置
                        self.hubstudio_api.timeout = old_timeout
                    except:
                        pass

                # 在后台线程中关闭，设置为daemon线程
                close_thread = threading.Thread(target=close_browser, daemon=True)
                close_thread.start()

                # 不等待线程完成，立即返回

            except Exception as e:
                self.logger.warning(f"后台关闭浏览器环境时出错: {e}")

            self.logger.info(f"快速断开连接完成: {container_code}")

        except Exception as e:
            self.logger.error(f"快速断开连接失败: {str(e)}")

    def emergency_disconnect(self, container_code: str) -> None:
        """
        紧急断开连接，用于极端情况

        Args:
            container_code: 环境ID
        """
        try:
            self.logger.critical(f"紧急断开浏览器连接: {container_code}")

            # 立即清理所有资源，不进行任何等待
            self.driver = None
            self.wait = None

            self.logger.info(f"紧急断开连接完成: {container_code}")

        except:
            pass
    
    def _find_element_by_selectors(self, selectors: List[str], timeout: Optional[int] = None) -> Any:
        """
        使用多个选择器尝试查找元素
        
        Args:
            selectors: 选择器列表
            timeout: 超时时间
            
        Returns:
            找到的元素
            
        Raises:
            TimeoutException: 所有选择器都未找到元素
        """
        if not self.wait:
            raise YouTubeUploaderError("WebDriver未初始化")
            
        wait_time = timeout or self.wait_timeout
        
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, wait_time).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                self.logger.debug(f"使用选择器找到元素: {selector}")
                return element
            except TimeoutException:
                continue
        
        raise TimeoutException(f"使用所有选择器都未找到元素: {selectors}")
    
    def _safe_click(self, element: Any, max_retries: int = 3) -> bool:
        """
        安全点击元素，包含重试机制
        
        Args:
            element: 要点击的元素
            max_retries: 最大重试次数
            
        Returns:
            是否点击成功
        """
        for attempt in range(max_retries):
            try:
                # 滚动到元素可见
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(0.5)
                
                # 尝试点击
                element.click()
                return True
                
            except ElementNotInteractableException:
                if attempt < max_retries - 1:
                    self.logger.warning(f"元素不可交互，重试 {attempt + 1}/{max_retries}")
                    time.sleep(1)
                else:
                    self.logger.error("元素点击失败，已达到最大重试次数")
                    return False
            except Exception as e:
                self.logger.warning(f"点击元素时出错: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(1)
                else:
                    return False
        
        return False

    def navigate_to_youtube_studio(self) -> bool:
        """
        导航到YouTube Studio，包含完整的登录检测和页面验证

        Returns:
            是否导航成功
        """
        max_retries = 3

        for attempt in range(max_retries):
            try:
                if not self.driver:
                    raise YouTubeUploaderError("WebDriver未初始化")

                self.logger.info(f"🌐 导航到YouTube Studio (尝试 {attempt + 1}/{max_retries})")

                # 检查WebDriver连接状态
                try:
                    current_url = self.driver.current_url
                    self.logger.debug(f"WebDriver当前状态正常，URL: {current_url}")
                except Exception as e:
                    self.logger.error(f"WebDriver连接已断开: {e}")
                    return False

                # 导航到YouTube Studio
                self.logger.info(f"正在访问: {self.YOUTUBE_STUDIO_URL}")
                try:
                    self.driver.get(self.YOUTUBE_STUDIO_URL)
                except Exception as e:
                    self.logger.error(f"导航尝试 {attempt + 1} 失败: {e}")
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(5)  # 等待更长时间再重试
                        continue
                    else:
                        return False

                # 等待页面开始加载
                self.logger.debug("等待页面开始加载...")
                import time
                time.sleep(5)  # 增加等待时间

                # 等待页面完全加载
                self.logger.debug("等待页面完全加载...")
                try:
                    # 使用更长的超时时间
                    WebDriverWait(self.driver, 15).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )
                except Exception as e:
                    self.logger.warning(f"页面加载状态检测超时: {e}，继续执行...")

                # 获取当前页面信息
                try:
                    current_url = self.driver.current_url
                    page_title = self.driver.title

                    self.logger.info(f"📍 当前URL: {current_url}")
                    self.logger.info(f"📄 页面标题: {page_title}")

                    # 检查是否成功到达YouTube相关页面
                    if "youtube.com" not in current_url.lower():
                        self.logger.warning(f"未到达YouTube页面，当前URL: {current_url}")
                        if attempt < max_retries - 1:
                            continue
                        else:
                            return False

                except Exception as e:
                    self.logger.error(f"获取页面信息失败: {e}")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return False

                # 检查登录状态
                login_status = self._check_login_status()

                if login_status == "needs_login":
                    self.logger.warning("🔐 检测到需要登录Google账户")
                    self.logger.info("请在浏览器中手动完成登录，然后点击程序中的'继续'按钮")

                    # 等待用户手动登录
                    if not self._wait_for_manual_login():
                        return False

                    # 重新检查登录状态
                    login_status = self._check_login_status()

                if login_status == "logged_in":
                    # 验证YouTube Studio页面
                    if self._verify_youtube_studio_page():
                        self.logger.success("✅ 成功导航到YouTube Studio并验证页面")
                        return True
                    else:
                        self.logger.warning("⚠️ YouTube Studio页面验证失败")
                        if attempt < max_retries - 1:
                            continue

                elif login_status == "error":
                    self.logger.error("❌ 登录状态检测出错")
                    if attempt < max_retries - 1:
                        continue

                # 如果到这里说明失败了
                if attempt == max_retries - 1:
                    self.logger.error("导航到YouTube Studio失败")
                    return False

            except Exception as e:
                self.logger.error(f"导航尝试 {attempt + 1} 失败: {str(e)}")

                # 检查是否是致命错误
                if self._is_fatal_navigation_error(e):
                    self.logger.error("检测到致命导航错误，停止重试")
                    self._handle_navigation_failure()
                    return False

                if attempt < max_retries - 1:
                    self.logger.info("等待3秒后重试...")
                    import time
                    time.sleep(3)
                else:
                    self.logger.error("导航到YouTube Studio失败，已尝试所有重试")
                    self._handle_navigation_failure()
                    return False

    def _is_fatal_navigation_error(self, error: Exception) -> bool:
        """检查是否是致命的导航错误"""
        error_str = str(error).lower()

        # 致命错误类型
        fatal_errors = [
            "webdriver", "connection refused", "chrome not reachable",
            "session not created", "invalid session", "no such window"
        ]

        for fatal_error in fatal_errors:
            if fatal_error in error_str:
                return True

        return False

    def _handle_navigation_failure(self) -> None:
        """处理导航失败的情况"""
        try:
            self.logger.warning("正在处理导航失败...")

            # 尝试获取当前页面信息用于调试
            try:
                if self.driver:
                    current_url = self.driver.current_url
                    page_title = self.driver.title
                    self.logger.info(f"失败时的URL: {current_url}")
                    self.logger.info(f"失败时的标题: {page_title}")
            except:
                self.logger.warning("无法获取失败时的页面信息")

            # 在并发环境中，标记此环境为不可用
            self.logger.error("此浏览器环境将被标记为不可用")

        except Exception as e:
            self.logger.error(f"处理导航失败时出错: {e}")

    def upload_video_automated(
        self,
        video_path: str,
        title: str,
        description: str = "",
        visibility: str = "public",
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> bool:
        """
        完全自动化的视频上传流程

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            visibility: 可见性设置 (public, unlisted, private)
            progress_callback: 进度回调函数

        Returns:
            bool: 上传是否成功
        """
        try:
            self.logger.info(f"开始自动上传视频: {video_path}")

            # 步骤1: 点击"上传视频"按钮
            if not self._click_upload_button():
                return False

            if progress_callback:
                progress_callback(10.0)

            # 步骤2: 上传视频文件
            if not self._upload_video_file(video_path):
                return False

            if progress_callback:
                progress_callback(30.0)

            # 步骤3: 输入标题和说明
            if not self._fill_video_details(title, description):
                return False

            if progress_callback:
                progress_callback(50.0)

            # 步骤4: 设置儿童内容选项
            if not self._set_child_content_option():
                return False

            if progress_callback:
                progress_callback(60.0)

            # 步骤5-7: 点击三次"下一步"
            for step in range(1, 4):
                if not self._click_next_button(step):
                    return False
                if progress_callback:
                    progress_callback(60.0 + step * 10.0)

            # 步骤8: 选择"公开"
            if not self._set_visibility(visibility):
                return False

            if progress_callback:
                progress_callback(90.0)

            # 步骤9: 点击"发布"
            if not self._click_publish_button():
                return False

            if progress_callback:
                progress_callback(100.0)

            self.logger.success(f"视频上传成功: {title}")
            return True

        except Exception as e:
            self.logger.error(f"自动上传失败: {e}")
            return False

    def _click_upload_button(self) -> bool:
        """点击"上传视频"按钮（增强调试版）"""
        try:
            self.logger.info("开始查找上传按钮...")

            # 首先检查页面状态
            self._debug_page_state()

            # 等待页面完全加载
            self.logger.info("等待页面完全加载...")
            time.sleep(3)

            # 2024年最新的YouTube Studio上传按钮选择器
            upload_selectors = [
                # 最新的YouTube Studio界面选择器（2024年）
                "//button[@aria-label='建立']",
                "//button[@aria-label='Create']",
                "//button[@aria-label='上傳影片']",
                "//button[@aria-label='Upload video']",
                "//button[@aria-label='上传视频']",

                # 基于文本内容的选择器
                "//button[contains(text(), '建立')]",
                "//button[contains(text(), 'CREATE')]",
                "//button[contains(text(), '上傳影片')]",
                "//button[contains(text(), '上传视频')]",
                "//button[contains(text(), 'Upload video')]",
                "//span[contains(text(), '建立')]/parent::button",
                "//span[contains(text(), 'CREATE')]/parent::button",
                "//span[contains(text(), '上傳影片')]/parent::button",

                # 基于图标和类名的选择器
                "//ytcp-icon-button[@icon='add']//button",
                "//ytcp-icon-button[contains(@class, 'style-scope')]//button",
                "//button[contains(@class, 'ytcp-icon-button')]",
                "//tp-yt-iron-icon[@icon='add']/parent::button",
                "//tp-yt-iron-icon[@icon='add']/../..",
                "//yt-icon[@class='ytcp-icon-button-icon']/parent::button",
                "//yt-icon[contains(@class, 'ytcp-icon')]/parent::button",

                # 基于ID和特定位置的选择器
                "//*[@id='create-icon']",
                "//*[@id='create-button']",
                "//div[@id='buttons']//button[1]",
                "//div[@id='buttons']//ytcp-icon-button[1]//button",

                # 基于容器和层级的选择器
                "//ytcp-app-header//button[contains(@class, 'style-scope')]",
                "//ytcp-app-header//ytcp-icon-button//button",
                "//header//button[contains(@aria-label, 'Create') or contains(@aria-label, '建立')]",
                "//ytcp-topbar-menu-button-renderer//button",

                # 更通用的选择器（作为备选）
                "//button[contains(@class, 'style-scope ytcp-icon-button')]",
                "//ytcp-button[contains(@class, 'create-button')]//button",
                "//button[contains(@class, 'create-button')]",

                # 基于SVG图标的选择器
                "//button[.//svg]",
                "//button[contains(@class, 'yt-spec-button-shape-next')]",
                "//button[contains(@class, 'yt-spec-touch-feedback-shape')]"
            ]

            self.logger.info(f"尝试 {len(upload_selectors)} 种不同的选择器...")

            upload_button = None
            successful_selector = None

            for i, selector in enumerate(upload_selectors):
                try:
                    self.logger.debug(f"尝试选择器 {i+1}/{len(upload_selectors)}: {selector}")

                    # 使用较短的超时时间逐个尝试
                    upload_button = WebDriverWait(self.driver, 2).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )

                    successful_selector = selector
                    self.logger.info(f"✅ 找到上传按钮: {selector}")
                    break

                except TimeoutException:
                    self.logger.debug(f"选择器超时: {selector}")
                    continue
                except Exception as e:
                    self.logger.debug(f"选择器错误: {selector} - {e}")
                    continue

            if not upload_button:
                self.logger.error("❌ 未找到上传按钮")
                self._debug_available_elements()
                return False

            # 尝试多种点击方式
            click_success = self._try_multiple_click_methods(upload_button, successful_selector)

            if click_success:
                self.logger.info("✅ 上传按钮点击成功")
                # 等待上传对话框出现
                return self._wait_for_upload_dialog()
            else:
                self.logger.error("❌ 上传按钮点击失败")
                return False

        except Exception as e:
            self.logger.error(f"点击上传按钮异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _debug_page_state(self) -> None:
        """调试页面状态"""
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            self.logger.info(f"🔍 当前页面URL: {current_url}")
            self.logger.info(f"🔍 页面标题: {page_title}")

            # 检查页面是否为YouTube Studio
            if "studio.youtube.com" not in current_url:
                self.logger.warning("⚠️ 当前不在YouTube Studio页面")

            # 检查页面加载状态
            ready_state = self.driver.execute_script("return document.readyState")
            self.logger.info(f"🔍 页面加载状态: {ready_state}")

            # 检查是否有错误信息
            error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), 'error') or contains(text(), '错误')]")
            if error_elements:
                self.logger.warning(f"⚠️ 页面包含错误信息: {len(error_elements)} 个")

        except Exception as e:
            self.logger.warning(f"调试页面状态失败: {e}")

    def _debug_available_elements(self) -> None:
        """调试可用元素"""
        try:
            self.logger.info("🔍 分析页面中的可点击按钮...")

            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            self.logger.info(f"找到 {len(buttons)} 个按钮元素")

            # 分析前10个按钮
            for i, button in enumerate(buttons[:10]):
                try:
                    text = button.text.strip()
                    aria_label = button.get_attribute("aria-label") or ""
                    class_name = button.get_attribute("class") or ""

                    self.logger.debug(f"按钮 {i+1}: text='{text}', aria-label='{aria_label}', class='{class_name[:50]}...'")

                    # 检查是否可能是上传按钮
                    if any(keyword in (text + aria_label).lower() for keyword in ['create', 'upload', '上传', '上傳', '建立']):
                        self.logger.info(f"🎯 可能的上传按钮: text='{text}', aria-label='{aria_label}'")

                except Exception as e:
                    self.logger.debug(f"分析按钮 {i+1} 失败: {e}")

        except Exception as e:
            self.logger.warning(f"调试可用元素失败: {e}")

    def _try_multiple_click_methods(self, element, selector: str) -> bool:
        """尝试多种点击方式"""
        try:
            self.logger.info("尝试多种点击方式...")

            # 方法1: JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", element)
                self.logger.info("✅ JavaScript点击成功")
                time.sleep(1)
                return True
            except Exception as e:
                self.logger.debug(f"JavaScript点击失败: {e}")

            # 方法2: 普通点击
            try:
                element.click()
                self.logger.info("✅ 普通点击成功")
                time.sleep(1)
                return True
            except Exception as e:
                self.logger.debug(f"普通点击失败: {e}")

            # 方法3: 滚动到元素后点击
            try:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(0.5)
                element.click()
                self.logger.info("✅ 滚动后点击成功")
                time.sleep(1)
                return True
            except Exception as e:
                self.logger.debug(f"滚动后点击失败: {e}")

            # 方法4: ActionChains点击
            try:
                from selenium.webdriver.common.action_chains import ActionChains
                actions = ActionChains(self.driver)
                actions.move_to_element(element).click().perform()
                self.logger.info("✅ ActionChains点击成功")
                time.sleep(1)
                return True
            except Exception as e:
                self.logger.debug(f"ActionChains点击失败: {e}")

            self.logger.error("❌ 所有点击方式都失败了")
            return False

        except Exception as e:
            self.logger.error(f"尝试点击方式时出错: {e}")
            return False

    def _wait_for_upload_dialog(self) -> bool:
        """等待上传对话框出现"""
        try:
            self.logger.info("等待上传对话框出现...")

            # 上传对话框的可能选择器
            dialog_selectors = [
                "//div[contains(@class, 'upload-dialog')]",
                "//div[contains(@class, 'ytcp-uploads-dialog')]",
                "//div[@role='dialog']",
                "//ytcp-uploads-dialog",
                "//div[contains(text(), '上傳影片') or contains(text(), 'Upload video')]",
                "//input[@type='file']",
                "//button[contains(text(), '選取檔案') or contains(text(), 'SELECT FILES')]"
            ]

            for selector in dialog_selectors:
                try:
                    WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    self.logger.info(f"✅ 检测到上传对话框: {selector}")
                    return True
                except:
                    continue

            self.logger.warning("⚠️ 未检测到上传对话框，但继续执行")
            time.sleep(2)
            return True

        except Exception as e:
            self.logger.error(f"等待上传对话框失败: {e}")
            return False

    def _upload_video_file(self, video_path: str) -> bool:
        """上传视频文件（增强调试版）"""
        try:
            self.logger.info(f"开始上传视频文件: {video_path}")

            # 验证文件存在
            if not os.path.exists(video_path):
                self.logger.error(f"❌ 视频文件不存在: {video_path}")
                return False

            file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
            self.logger.info(f"📁 文件大小: {file_size:.2f} MB")

            # 等待页面稳定
            time.sleep(2)

            # 2024年最新的YouTube Studio文件输入框选择器
            file_input_selectors = [
                # 最常见的文件输入框选择器
                "//input[@type='file']",
                "//input[@accept*='video']",
                "//input[@accept*='*']",
                "//input[contains(@accept, '.mp4')]",
                "//input[contains(@accept, '.avi')]",

                # YouTube Studio特定的选择器
                "//ytcp-uploads-file-picker//input[@type='file']",
                "//ytcp-uploads-dialog//input[@type='file']",
                "//ytcp-uploads-dialog//input",
                "//div[@id='upload-dialog']//input[@type='file']",
                "//div[contains(@class, 'upload-dialog')]//input[@type='file']",

                # 更具体的容器选择器
                "//ytcp-uploads-file-picker//input",
                "//div[contains(@class, 'file-picker')]//input[@type='file']",
                "//div[contains(@class, 'upload-area')]//input[@type='file']",
                "//form//input[@type='file']"
            ]

            self.logger.info("🔍 查找文件输入框...")
            file_input = None

            for i, selector in enumerate(file_input_selectors):
                try:
                    self.logger.debug(f"尝试文件输入选择器 {i+1}: {selector}")
                    file_input = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    self.logger.info(f"✅ 找到文件输入框: {selector}")
                    break
                except:
                    continue

            if not file_input:
                self.logger.warning("⚠️ 未直接找到文件输入框，尝试点击选择文件按钮...")

                # 2024年最新的选择文件按钮选择器
                select_file_selectors = [
                    # 中文界面选择器
                    "//button[contains(text(), '選取檔案')]",
                    "//button[contains(text(), '选择文件')]",
                    "//button[contains(text(), '選擇檔案')]",
                    "//span[contains(text(), '選取檔案')]/parent::button",
                    "//span[contains(text(), '选择文件')]/parent::button",

                    # 英文界面选择器
                    "//button[contains(text(), 'SELECT FILES')]",
                    "//button[contains(text(), 'Select files')]",
                    "//button[contains(text(), 'Choose files')]",
                    "//span[contains(text(), 'SELECT FILES')]/parent::button",
                    "//span[contains(text(), 'Select files')]/parent::button",

                    # 基于类名和ID的选择器
                    "//ytcp-button[contains(@class, 'select-files-button')]//button",
                    "//button[contains(@class, 'select-files')]",
                    "//div[contains(@class, 'upload-area')]//button",
                    "//div[contains(@class, 'file-picker')]//button",

                    # 更通用的选择器
                    "//ytcp-uploads-file-picker//button",
                    "//ytcp-uploads-dialog//button[1]",
                    "//div[@id='upload-dialog']//button[1]"
                ]

                for i, selector in enumerate(select_file_selectors):
                    try:
                        self.logger.debug(f"尝试选择文件按钮 {i+1}: {selector}")
                        select_button = WebDriverWait(self.driver, 2).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )

                        self.logger.info(f"✅ 找到选择文件按钮: {selector}")
                        self.driver.execute_script("arguments[0].click();", select_button)
                        time.sleep(1)

                        # 再次查找文件输入框
                        try:
                            file_input = self.driver.find_element(By.XPATH, "//input[@type='file']")
                            self.logger.info("✅ 点击后找到文件输入框")
                            break
                        except:
                            continue
                    except:
                        continue

            if not file_input:
                self.logger.error("❌ 未找到文件输入框")
                self._debug_upload_dialog()
                return False

            # 上传文件
            try:
                absolute_path = os.path.abspath(video_path)
                self.logger.info(f"📤 开始上传文件: {absolute_path}")

                file_input.send_keys(absolute_path)
                self.logger.info("✅ 文件路径已发送到输入框")

                # 等待上传开始
                time.sleep(3)

                # 检查上传是否开始
                if self._check_upload_started():
                    self.logger.info("✅ 上传已开始")
                    # 等待上传完成
                    return self._wait_for_upload_complete()
                else:
                    self.logger.error("❌ 上传未开始")
                    return False

            except Exception as e:
                self.logger.error(f"❌ 文件上传过程出错: {e}")
                return False

        except Exception as e:
            self.logger.error(f"上传视频文件异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    def _debug_upload_dialog(self) -> None:
        """调试上传对话框"""
        try:
            self.logger.info("🔍 分析上传对话框内容...")

            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            self.logger.info(f"找到 {len(inputs)} 个输入框")

            for i, input_elem in enumerate(inputs):
                try:
                    input_type = input_elem.get_attribute("type")
                    accept = input_elem.get_attribute("accept") or ""
                    self.logger.debug(f"输入框 {i+1}: type='{input_type}', accept='{accept}'")
                except:
                    pass

            # 查找所有按钮
            buttons = self.driver.find_elements(By.TAG_NAME, "button")
            self.logger.info(f"找到 {len(buttons)} 个按钮")

            for i, button in enumerate(buttons[:5]):  # 只检查前5个
                try:
                    text = button.text.strip()
                    if text:
                        self.logger.debug(f"按钮 {i+1}: '{text}'")
                except:
                    pass

        except Exception as e:
            self.logger.warning(f"调试上传对话框失败: {e}")

    def _check_upload_started(self) -> bool:
        """检查上传是否开始"""
        try:
            # 检查上传进度指示器
            progress_selectors = [
                "//div[contains(@class, 'progress')]",
                "//ytcp-video-upload-progress",
                "//*[contains(text(), '正在上傳') or contains(text(), 'Uploading')]",
                "//div[contains(@class, 'upload-progress')]"
            ]

            for selector in progress_selectors:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        self.logger.info(f"✅ 检测到上传进度: {selector}")
                        return True
                except:
                    continue

            # 检查页面URL是否变化
            current_url = self.driver.current_url
            if "upload" in current_url.lower():
                self.logger.info("✅ URL显示进入上传流程")
                return True

            return False

        except Exception as e:
            self.logger.warning(f"检查上传状态失败: {e}")
            return True  # 假设上传已开始

    def _wait_for_upload_complete(self) -> bool:
        """等待视频上传完成（增强版）"""
        try:
            self.logger.info("⏳ 等待视频上传完成...")

            # 等待上传进度条出现
            time.sleep(5)

            # 等待上传完成（最多等待15分钟）
            max_wait_time = 900  # 15分钟
            start_time = time.time()
            last_progress_check = 0

            while time.time() - start_time < max_wait_time:
                try:
                    elapsed_time = time.time() - start_time

                    # 每30秒报告一次进度
                    if elapsed_time - last_progress_check > 30:
                        self.logger.info(f"⏳ 上传进行中... 已等待 {elapsed_time/60:.1f} 分钟")
                        last_progress_check = elapsed_time

                    # 检查上传完成的多种指示器
                    if self._check_upload_completion():
                        self.logger.info("✅ 检测到上传完成")
                        break

                    # 检查是否有错误
                    if self._check_upload_error():
                        self.logger.error("❌ 检测到上传错误")
                        return False

                    time.sleep(5)

                except Exception as e:
                    self.logger.warning(f"检查上传状态时出错: {e}")
                    time.sleep(5)

            if time.time() - start_time >= max_wait_time:
                self.logger.warning("⚠️ 上传等待超时，但继续执行后续步骤")

            self.logger.info("✅ 视频上传阶段完成")
            return True

        except Exception as e:
            self.logger.error(f"等待上传完成异常: {e}")
            return False

    def _check_upload_completion(self) -> bool:
        """检查上传是否完成"""
        try:
            # 检查上传完成的指示器
            completion_indicators = [
                # 进度条消失
                "//div[contains(@class, 'progress')]",
                "//ytcp-video-upload-progress",

                # 上传中文本消失
                "//*[contains(text(), '正在上傳') or contains(text(), 'Uploading') or contains(text(), '上传中')]",

                # 出现详情页面元素
                "//input[@aria-label='標題' or @aria-label='标题' or @aria-label='Title']",
                "//div[@aria-label='說明' or @aria-label='描述' or @aria-label='Description']",

                # 出现下一步按钮
                "//button[contains(text(), '下一步') or contains(text(), 'Next')]"
            ]

            # 检查进度条和上传文本是否消失
            progress_gone = True
            for selector in completion_indicators[:2]:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    if elements and any(elem.is_displayed() for elem in elements):
                        progress_gone = False
                        break
                except:
                    pass

            # 检查详情页面元素是否出现
            details_appeared = False
            for selector in completion_indicators[2:]:
                try:
                    element = self.driver.find_element(By.XPATH, selector)
                    if element.is_displayed():
                        details_appeared = True
                        break
                except:
                    pass

            # 如果进度消失且详情页面出现，认为上传完成
            if progress_gone and details_appeared:
                return True

            # 检查URL变化
            current_url = self.driver.current_url
            if "details" in current_url.lower() or "edit" in current_url.lower():
                return True

            return False

        except Exception as e:
            self.logger.warning(f"检查上传完成状态失败: {e}")
            return False

    def _check_upload_error(self) -> bool:
        """检查上传是否出错"""
        try:
            error_indicators = [
                "//*[contains(text(), 'error') or contains(text(), '错误') or contains(text(), '錯誤')]",
                "//*[contains(text(), 'failed') or contains(text(), '失败') or contains(text(), '失敗')]",
                "//*[contains(text(), '處理程序已中斷') or contains(text(), '处理程序已中断')]",
                "//*[contains(text(), 'Processing interrupted') or contains(text(), 'Upload failed')]",
                "//*[contains(text(), '無法處理') or contains(text(), '无法处理')]",
                "//div[contains(@class, 'error')]",
                "//div[@role='alert']"
            ]

            for selector in error_indicators:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        if element.is_displayed():
                            error_text = element.text.strip()
                            if error_text:
                                # 检查是否是文件格式问题
                                if any(keyword in error_text for keyword in ['處理程序已中斷', '处理程序已中断', 'Processing interrupted']):
                                    self.logger.warning(f"⚠️ 检测到文件处理问题: {error_text}")
                                    self.logger.info("💡 建议：使用有效的视频文件进行测试")
                                    return True
                                else:
                                    self.logger.error(f"❌ 检测到错误: {error_text}")
                                    return True
                except:
                    pass

            return False

        except Exception as e:
            self.logger.warning(f"检查上传错误失败: {e}")
            return False

    def _fill_video_details(self, title: str, description: str) -> bool:
        """填写视频标题和描述（增强版）"""
        try:
            self.logger.info("📝 开始填写视频详情...")

            # 等待页面加载
            time.sleep(3)

            # 填写标题
            if not self._fill_title(title):
                self.logger.error("❌ 标题填写失败")
                return False

            # 填写描述
            if description and not self._fill_description(description):
                self.logger.warning("⚠️ 描述填写失败，但继续执行")

            self.logger.info("✅ 视频详情填写完成")
            return True

        except Exception as e:
            self.logger.error(f"填写视频详情异常: {e}")
            return False

    def _fill_title(self, title: str) -> bool:
        """填写视频标题"""
        try:
            self.logger.info(f"📝 填写标题: {title}")

            # 更全面的标题输入框选择器
            title_selectors = [
                "//input[@aria-label='標題 (必填)']",
                "//input[@aria-label='标题 (必填)']",
                "//input[@aria-label='Title (required)']",
                "//input[@aria-label='標題']",
                "//input[@aria-label='标题']",
                "//input[@aria-label='Title']",
                "//ytcp-social-suggestions-textbox[@label='標題']//input",
                "//ytcp-social-suggestions-textbox[@label='标题']//input",
                "//div[@id='textbox']//input",
                "//input[contains(@class, 'title')]",
                "//input[@placeholder*='标题' or @placeholder*='標題' or @placeholder*='Title']",
                "//div[contains(@class, 'title')]//input"
            ]

            title_input = None

            for i, selector in enumerate(title_selectors):
                try:
                    self.logger.debug(f"尝试标题选择器 {i+1}: {selector}")
                    title_input = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.logger.info(f"✅ 找到标题输入框: {selector}")
                    break
                except:
                    continue

            if not title_input:
                self.logger.error("❌ 未找到标题输入框")
                self._debug_form_elements()
                return False

            # 多种方式填写标题
            try:
                # 方法1: 清空并输入
                title_input.clear()
                title_input.send_keys(title)
                self.logger.info("✅ 标题填写成功（方法1）")

                # 验证标题是否正确填写
                current_value = title_input.get_attribute("value")
                if current_value == title:
                    self.logger.info(f"✅ 标题验证成功: {current_value}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 标题验证失败，期望: {title}, 实际: {current_value}")

            except Exception as e:
                self.logger.warning(f"方法1失败: {e}")

            # 方法2: JavaScript设置
            try:
                self.driver.execute_script("arguments[0].value = arguments[1];", title_input, title)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", title_input)
                self.logger.info("✅ 标题填写成功（方法2）")
                return True
            except Exception as e:
                self.logger.warning(f"方法2失败: {e}")

            # 方法3: 逐字符输入
            try:
                title_input.clear()
                for char in title:
                    title_input.send_keys(char)
                    time.sleep(0.1)
                self.logger.info("✅ 标题填写成功（方法3）")
                return True
            except Exception as e:
                self.logger.warning(f"方法3失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"填写标题异常: {e}")
            return False

    def _fill_description(self, description: str) -> bool:
        """填写视频描述"""
        try:
            self.logger.info(f"📝 填写描述: {description[:50]}...")

            # 描述输入框选择器
            desc_selectors = [
                "//div[@aria-label='說明']",
                "//div[@aria-label='描述']",
                "//div[@aria-label='Description']",
                "//textarea[@aria-label='說明']",
                "//textarea[@aria-label='描述']",
                "//textarea[@aria-label='Description']",
                "//ytcp-social-suggestions-textbox[@label='說明']//div[@contenteditable='true']",
                "//ytcp-social-suggestions-textbox[@label='描述']//div[@contenteditable='true']",
                "//div[contains(@class, 'description')]//div[@contenteditable='true']",
                "//div[@contenteditable='true'][contains(@aria-label, '說明') or contains(@aria-label, '描述')]"
            ]

            desc_input = None
            for i, selector in enumerate(desc_selectors):
                try:
                    self.logger.debug(f"尝试描述选择器 {i+1}: {selector}")
                    desc_input = WebDriverWait(self.driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.logger.info(f"✅ 找到描述输入框: {selector}")
                    break
                except:
                    continue

            if not desc_input:
                self.logger.warning("⚠️ 未找到描述输入框")
                return False

            # 填写描述
            try:
                # 对于contenteditable元素
                if desc_input.get_attribute("contenteditable") == "true":
                    self.driver.execute_script("arguments[0].innerHTML = arguments[1];", desc_input, description)
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", desc_input)
                else:
                    # 对于textarea元素
                    desc_input.clear()
                    desc_input.send_keys(description)

                self.logger.info("✅ 描述填写成功")
                return True

            except Exception as e:
                self.logger.warning(f"描述填写失败: {e}")
                return False

        except Exception as e:
            self.logger.error(f"填写描述异常: {e}")
            return False

    def _debug_form_elements(self) -> None:
        """调试表单元素"""
        try:
            self.logger.info("🔍 分析表单元素...")

            # 查找所有输入框
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            self.logger.info(f"找到 {len(inputs)} 个input元素")

            for i, input_elem in enumerate(inputs[:5]):  # 只检查前5个
                try:
                    input_type = input_elem.get_attribute("type")
                    aria_label = input_elem.get_attribute("aria-label") or ""
                    placeholder = input_elem.get_attribute("placeholder") or ""
                    self.logger.debug(f"Input {i+1}: type='{input_type}', aria-label='{aria_label}', placeholder='{placeholder}'")
                except:
                    pass

            # 查找所有可编辑div
            editables = self.driver.find_elements(By.XPATH, "//div[@contenteditable='true']")
            self.logger.info(f"找到 {len(editables)} 个可编辑div")

            for i, div in enumerate(editables[:3]):  # 只检查前3个
                try:
                    aria_label = div.get_attribute("aria-label") or ""
                    class_name = div.get_attribute("class") or ""
                    self.logger.debug(f"Editable {i+1}: aria-label='{aria_label}', class='{class_name[:50]}...'")
                except:
                    pass

        except Exception as e:
            self.logger.warning(f"调试表单元素失败: {e}")

    def _set_child_content_option(self) -> bool:
        """设置儿童内容选项"""
        try:
            self.logger.info("正在设置儿童内容选项...")

            # 查找儿童内容选项
            child_content_selectors = [
                "//tp-yt-paper-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
                "//paper-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']",
                "//input[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']/..",
                "//ytcp-radio-button[@name='VIDEO_MADE_FOR_KIDS_NOT_MFK']"
            ]

            # 默认选择"否，不是兒童專屬內容"
            for selector in child_content_selectors:
                try:
                    option = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.driver.execute_script("arguments[0].click();", option)
                    self.logger.info("已设置为非儿童专属内容")
                    return True
                except:
                    continue

            # 如果找不到具体选项，尝试查找包含文本的选项
            text_selectors = [
                "//span[contains(text(), '否，不是兒童專屬內容')]/..",
                "//span[contains(text(), '不是儿童专属内容')]/..",
                "//span[contains(text(), 'No, it')]/.."
            ]

            for selector in text_selectors:
                try:
                    option = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.driver.execute_script("arguments[0].click();", option)
                    self.logger.info("已设置为非儿童专属内容")
                    return True
                except:
                    continue

            self.logger.warning("未找到儿童内容选项，跳过此步骤")
            return True

        except Exception as e:
            self.logger.error(f"设置儿童内容选项失败: {e}")
            return False

    def _click_next_button(self, step: int) -> bool:
        """点击下一步按钮"""
        try:
            self.logger.info(f"正在点击第{step}次下一步...")

            # 下一步按钮的选择器
            next_selectors = [
                "//button[contains(text(), '下一步')]",
                "//button[contains(text(), 'Next')]",
                "//button[@aria-label='下一步']",
                "//button[@aria-label='Next']",
                "//ytcp-button[contains(@class, 'next-button')]//button",
                "//div[@id='next-button']//button"
            ]

            next_button = None
            for selector in next_selectors:
                try:
                    next_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except:
                    continue

            if not next_button:
                self.logger.error(f"未找到第{step}次下一步按钮")
                return False

            # 点击下一步
            self.driver.execute_script("arguments[0].click();", next_button)
            self.logger.info(f"已点击第{step}次下一步")

            # 等待页面加载
            time.sleep(3)
            return True

        except Exception as e:
            self.logger.error(f"点击第{step}次下一步失败: {e}")
            return False

    def _set_visibility(self, visibility: str) -> bool:
        """设置视频可见性"""
        try:
            self.logger.info(f"正在设置可见性为: {visibility}")

            # 可见性选项映射
            visibility_map = {
                "private": ["私人", "Private", "私密"],
                "unlisted": ["不公開", "Unlisted", "不公开"],
                "public": ["公開", "Public", "公开"]
            }

            target_texts = visibility_map.get(visibility, visibility_map["public"])

            # 查找可见性选项
            for text in target_texts:
                selectors = [
                    f"//tp-yt-paper-radio-button[contains(@name, '{text.upper()}')]",
                    f"//paper-radio-button[contains(@name, '{text.upper()}')]",
                    f"//span[contains(text(), '{text}')]/..",
                    f"//ytcp-radio-button[contains(@name, '{text.upper()}')]"
                ]

                for selector in selectors:
                    try:
                        option = self.wait.until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        self.driver.execute_script("arguments[0].click();", option)
                        self.logger.info(f"已设置可见性为: {visibility}")
                        return True
                    except:
                        continue

            # 如果找不到具体选项，尝试默认的公开选项
            default_selectors = [
                "//tp-yt-paper-radio-button[@name='PUBLIC']",
                "//paper-radio-button[@name='PUBLIC']",
                "//input[@value='PUBLIC']/.."
            ]

            for selector in default_selectors:
                try:
                    option = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.driver.execute_script("arguments[0].click();", option)
                    self.logger.info("已设置为公开")
                    return True
                except:
                    continue

            self.logger.warning("未找到可见性选项，使用默认设置")
            return True

        except Exception as e:
            self.logger.error(f"设置可见性失败: {e}")
            return False

    def _click_publish_button(self) -> bool:
        """点击发布按钮"""
        try:
            self.logger.info("正在点击发布按钮...")

            # 发布按钮的选择器
            publish_selectors = [
                "//button[contains(text(), '發布')]",
                "//button[contains(text(), '发布')]",
                "//button[contains(text(), 'Publish')]",
                "//button[@aria-label='發布']",
                "//button[@aria-label='发布']",
                "//button[@aria-label='Publish']",
                "//ytcp-button[contains(@class, 'publish-button')]//button",
                "//div[@id='done-button']//button"
            ]

            publish_button = None
            for selector in publish_selectors:
                try:
                    publish_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except:
                    continue

            if not publish_button:
                self.logger.error("未找到发布按钮")
                return False

            # 点击发布
            self.driver.execute_script("arguments[0].click();", publish_button)
            self.logger.info("已点击发布按钮")

            # 等待发布完成
            time.sleep(5)

            # 检查是否发布成功
            success_indicators = [
                "//span[contains(text(), '已發布')]",
                "//span[contains(text(), '已发布')]",
                "//span[contains(text(), 'Published')]",
                "//div[contains(text(), '影片已發布')]",
                "//div[contains(text(), '视频已发布')]"
            ]

            for indicator in success_indicators:
                try:
                    self.driver.find_element(By.XPATH, indicator)
                    self.logger.success("视频发布成功")
                    return True
                except:
                    continue

            self.logger.info("发布按钮已点击，等待确认...")
            time.sleep(3)
            return True

        except Exception as e:
            self.logger.error(f"点击发布按钮失败: {e}")
            return False

    def _check_login_status(self) -> str:
        """
        检查Google账户登录状态

        Returns:
            "logged_in": 已登录
            "needs_login": 需要登录
            "error": 检测出错
        """
        try:
            current_url = self.driver.current_url
            page_source = self.driver.page_source

            # 检查URL是否包含登录相关关键词
            if any(keyword in current_url.lower() for keyword in [
                "accounts.google.com", "signin", "login", "auth"
            ]):
                return "needs_login"

            # 检查页面内容是否包含登录相关元素
            if any(keyword in page_source.lower() for keyword in [
                "sign in", "登录", "sign up", "create account"
            ]):
                return "needs_login"

            # 检查是否在YouTube Studio域名
            if "studio.youtube.com" in current_url:
                return "logged_in"

            # 其他情况认为需要进一步检查
            return "needs_login"

        except Exception as e:
            self.logger.error(f"登录状态检测失败: {e}")
            return "error"

    def _wait_for_manual_login(self) -> bool:
        """
        等待用户手动登录

        Returns:
            是否登录成功
        """
        self.logger.info("等待用户手动登录...")

        # 这里可以添加GUI交互，让用户点击"继续"按钮
        # 暂时使用简单的等待机制
        import time
        max_wait_time = 300  # 最多等待5分钟
        check_interval = 5   # 每5秒检查一次

        for elapsed in range(0, max_wait_time, check_interval):
            time.sleep(check_interval)

            try:
                current_url = self.driver.current_url
                if "studio.youtube.com" in current_url:
                    self.logger.info("检测到已成功登录")
                    return True

                if elapsed % 30 == 0:  # 每30秒提示一次
                    self.logger.info(f"仍在等待登录... (已等待 {elapsed} 秒)")

            except Exception as e:
                self.logger.warning(f"登录检测出错: {e}")

        self.logger.error("等待登录超时")
        return False

    def _verify_youtube_studio_page(self) -> bool:
        """
        验证YouTube Studio页面是否正确加载

        Returns:
            是否验证成功
        """
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            # 检查URL
            if "studio.youtube.com" not in current_url:
                self.logger.warning(f"URL验证失败: {current_url}")
                return False

            # 检查页面标题
            if "youtube" not in page_title.lower() and "studio" not in page_title.lower():
                self.logger.warning(f"页面标题验证失败: {page_title}")
                return False

            # 检查关键页面元素
            try:
                # 等待YouTube Studio的关键元素加载
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support import expected_conditions as EC

                # 等待页面主要内容加载
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

                self.logger.info("YouTube Studio页面验证成功")
                return True

            except Exception as element_error:
                self.logger.warning(f"页面元素验证失败: {element_error}")
                return False

        except Exception as e:
            self.logger.error(f"页面验证失败: {e}")
            return False

    def upload_video(
        self,
        video_path: str,
        title: str,
        description: str = "",
        visibility: str = "public"
    ) -> bool:
        """
        上传视频到YouTube（使用新的自动化流程）

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            visibility: 可见性 (public, unlisted, private)

        Returns:
            是否上传成功
        """
        try:
            if not self.driver:
                raise YouTubeUploaderError("WebDriver未初始化")

            # 验证视频文件
            if not self._validate_video_file(video_path):
                return False

            self.logger.info(f"开始上传视频: {Path(video_path).name}")

            # 使用新的自动化上传流程
            return self.upload_video_automated(
                video_path=video_path,
                title=title,
                description=description,
                visibility=visibility
            )

        except Exception as e:
            self.logger.error(f"视频上传失败: {str(e)}")
            return False

    def _validate_video_file(self, video_path: str) -> bool:
        """
        验证视频文件

        Args:
            video_path: 视频文件路径

        Returns:
            是否有效
        """
        path = Path(video_path)

        if not path.exists():
            self.logger.error(f"视频文件不存在: {video_path}")
            return False

        if not path.is_file():
            self.logger.error(f"路径不是文件: {video_path}")
            return False

        if path.suffix.lower() not in self.SUPPORTED_FORMATS:
            self.logger.error(f"不支持的视频格式: {path.suffix}")
            return False

        # 检查文件大小 (YouTube限制为256GB，这里检查是否为空文件)
        if path.stat().st_size == 0:
            self.logger.error(f"视频文件为空: {video_path}")
            return False

        return True

    def _click_create_button(self) -> bool:
        """点击创建按钮"""
        try:
            self.logger.debug("查找并点击创建按钮")
            create_button = self._find_element_by_selectors(self.SELECTORS['create_button'])
            return self._safe_click(create_button)
        except TimeoutException:
            self.logger.error("未找到创建按钮")
            return False

    def _click_upload_video(self) -> bool:
        """点击上传视频选项"""
        try:
            self.logger.debug("查找并点击上传视频选项")
            upload_option = self._find_element_by_selectors(self.SELECTORS['upload_video'])
            return self._safe_click(upload_option)
        except TimeoutException:
            self.logger.error("未找到上传视频选项")
            return False

    def _select_video_file(self, video_path: str) -> bool:
        """选择视频文件"""
        try:
            self.logger.debug("查找文件输入框并上传文件")
            file_input = self._find_element_by_selectors(self.SELECTORS['file_input'])

            # 发送文件路径到文件输入框
            absolute_path = str(Path(video_path).absolute())
            file_input.send_keys(absolute_path)

            self.logger.info(f"已选择视频文件: {Path(video_path).name}")

            # 等待文件上传开始
            time.sleep(3)
            return True

        except TimeoutException:
            self.logger.error("未找到文件输入框")
            return False
        except Exception as e:
            self.logger.error(f"选择视频文件失败: {str(e)}")
            return False

    def _fill_video_info(self, title: str, description: str) -> bool:
        """填写视频信息"""
        try:
            # 填写标题
            self.logger.debug("填写视频标题")
            title_input = self._find_element_by_selectors(self.SELECTORS['title_input'])

            # 清空现有内容并输入新标题
            title_input.clear()
            title_input.send_keys(title)

            # 填写描述（如果提供）
            if description:
                self.logger.debug("填写视频描述")
                desc_input = self._find_element_by_selectors(self.SELECTORS['description_input'])
                desc_input.clear()
                desc_input.send_keys(description)

            self.logger.info(f"已填写视频信息 - 标题: {title}")
            return True

        except TimeoutException:
            self.logger.error("未找到标题或描述输入框")
            return False
        except Exception as e:
            self.logger.error(f"填写视频信息失败: {str(e)}")
            return False

    def _set_not_for_kids(self) -> bool:
        """设置不是专为儿童制作的内容"""
        try:
            self.logger.debug("设置儿童内容选项")
            not_for_kids_radio = self._find_element_by_selectors(self.SELECTORS['not_for_kids'])
            return self._safe_click(not_for_kids_radio)
        except TimeoutException:
            self.logger.error("未找到儿童内容选项")
            return False

    def _click_next_button(self) -> bool:
        """点击下一步按钮"""
        try:
            self.logger.debug("点击下一步按钮")
            next_button = self._find_element_by_selectors(self.SELECTORS['next_button'])
            return self._safe_click(next_button)
        except TimeoutException:
            self.logger.error("未找到下一步按钮")
            return False

    def _set_visibility(self, visibility: str) -> bool:
        """设置视频可见性"""
        try:
            if visibility.lower() == "public":
                self.logger.debug("设置视频为公开")
                public_radio = self._find_element_by_selectors(self.SELECTORS['public_radio'])
                return self._safe_click(public_radio)
            else:
                self.logger.warning(f"暂不支持的可见性设置: {visibility}，使用默认设置")
                return True
        except TimeoutException:
            self.logger.error("未找到可见性选项")
            return False

    def _click_publish_button(self) -> bool:
        """点击发布按钮"""
        try:
            self.logger.debug("点击发布按钮")
            publish_button = self._find_element_by_selectors(self.SELECTORS['publish_button'])
            return self._safe_click(publish_button)
        except TimeoutException:
            self.logger.error("未找到发布按钮")
            return False
