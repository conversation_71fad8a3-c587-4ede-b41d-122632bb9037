
# YouTube自动化上传故障排除指南

## 常见问题及解决方案

### 1. WebDriver连接问题
**症状**: "unrecognized chrome option" 错误
**解决方案**:
- 更新Chrome浏览器到最新版本
- 移除不兼容的Chrome选项
- 使用最简化的Chrome配置

### 2. Hub Studio连接问题  
**症状**: API连接失败，错误码E010006, E010009
**解决方案**:
- 确保Hub Studio正在运行
- 检查API端口6873是否可访问
- 重启Hub Studio服务

### 3. 元素定位失败
**症状**: 找不到上传按钮或文件输入框
**解决方案**:
- 使用最新的元素选择器
- 增加等待时间
- 检查页面是否完全加载

### 4. 文件上传失败
**症状**: 文件路径传递失败
**解决方案**:
- 使用绝对路径
- 检查文件权限
- 确保文件格式支持

## 调试步骤

1. 运行诊断工具: `python debug_youtube_upload.py`
2. 检查日志文件: `logs/youtube_uploader_errors.log`
3. 手动测试浏览器连接
4. 逐步测试上传流程

## 联系支持

如果问题仍然存在，请提供:
- 错误日志
- 系统环境信息
- 重现步骤
